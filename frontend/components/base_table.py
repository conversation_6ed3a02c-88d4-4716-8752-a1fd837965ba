import dash_ag_grid as dag
from typing import List, Dict, Union, Any, Optional

from frontend.utils.uuid_service import UUIDService


class BaseTable(dag.AgGrid):
    def __init__(
        self,
        id: Union[str, Dict[str, Any]] = UUIDService().generate_id(),
        column_defs: List[Dict[str, Any]] = [],
        row_data: List[Dict[str, Any]] = [],
        editable: Optional[bool] = True,
        resizable: Optional[bool] = False,
        sortable: Optional[bool] = False,
        grid_options: Dict[str, Any] = {},
        row_class_rules: Optional[Dict[str, Any]] = {},
    ) -> None:
        self.id = id
        self.column_defs = column_defs
        self.row_data = row_data
        self.editable = editable
        self.resizable = resizable
        self.sortable = sortable
        default_grid_options = {
            # "singleClickEdit": True,
            "tooltipShowDelay": 1000,
            "icons": {"menu": '<i class="fa fa-filter text-primary"></i>'},
            "rowHeight": 43,
        }
        self.grid_options = {**default_grid_options, **grid_options}

        super().__init__(
            id=self.id,
            rowData=self.row_data,
            columnDefs=self.column_defs,
            defaultColDef={
                "editable": self.editable,
                "resizable": self.resizable,
                "sortable": self.sortable,
                "suppressMovable": True,
                "cellClass": "overflow-hidden",
            },
            getRowId="params.data.id",
            columnSize="sizeToFit",
            dashGridOptions=self.grid_options,
            rowClassRules=row_class_rules,
        )
