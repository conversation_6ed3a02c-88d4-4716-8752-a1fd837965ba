from time import sleep
from click import style
from dash import html, dcc, callback, Input, Output, State, no_update, ctx, Patch
from dash.development.base_component import Component
from collections import defaultdict
from datetime import datetime
from pydantic import ValidationError
import logging, json

from frontend.utils.api_service import APIServiceProvider
from frontend.utils.url_encoder_service import UrlEncoderService
from frontend.utils.uuid_service import UUIDService
from frontend.utils.toast_manager import ToastManager
from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.base_container import Container
from frontend.components.frame import Frame
from frontend.components.base_dropdown import Dropdown
from frontend.components.base_spinner import Spinner
from frontend.components.base_input import InputField
from frontend.components.base_modal import Modal
from frontend.components.base_modal_body import ModalBody
from frontend.components.base_modal_footer import ModalFooter
from frontend.components.base_button import Button
from frontend.components.base_progress_bar import ProgressBar
from frontend.components.material_flow_table import material_flow_table
from frontend.components.entity_settings_table import entity_settings_table
from frontend.utils.enums import StreamType, EntityType, EquipmentType
from .setpoints_table import setpoints_table
from frontend.utils.auth import User


from typing import Dict, Any, Set, Tuple, List, Union
from pprint import pprint
import pandas as pd


api = APIServiceProvider().get_api_service()
logger = logging.getLogger()
DEFAULT_SECONDS = 10 * 60  # Time that the progress bar is set for 100%
BOUNDS_PERCENTAGE = 5  # Percentage +/- from var value for setpoint/condition ranges
VARIABLE_PRECISION = 3  # Num of decimal places for setpoint and condition values/ranges


experiment_type_options = [
    {
        "label": "Simulate and predict impact on KPIs by changing process conditions",
        "value": "Simulate and predict impact on KPIs by changing process conditions",
    }
]


def experiment_setup_section() -> Component:
    return Container(
        children=[
            html.H2("Experimentation Setup", className="my-2 fw-semibold"),
            Frame(
                [
                    Dropdown(
                        [],
                        id="plant-config-select",
                        className="text-dark h5",
                        clearable=False,
                    )
                ],
                title="Choose your model...",
                id="plant-config-select-frame",
            ),
            Frame(
                [
                    Dropdown(
                        experiment_type_options,
                        id="experiment-type-select",
                        className="text-dark h5",
                        clearable=False,
                    )
                ],
                title="I am looking to...",
                id="experiment-type-select-frame",
                style={"display": "none"},
            ),
            html.Div(
                [
                    material_flow_table(id="material-flow-table"),
                    entity_settings_table(id="entity-settings-table"),
                ],
                id="experiment-setup-tables",
                style={"display": "none"},
            ),
            Modal(
                id="setpoints-modal",
                children=[
                    ModalBody(
                        [
                            setpoints_table(
                                id="setpoints-table", title="Define Setpoints"
                            )
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="setpoints-cancel-button",
                                children=[
                                    "Cancel",
                                ],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="setpoints-save-button",
                                children=[
                                    "Save",
                                ],
                                className="bg-primary text-secondary rounded-3",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
                size="lg",
            ),
            Modal(
                id="conditions-modal",
                children=[
                    ModalBody(
                        [
                            setpoints_table(
                                id="conditions-table", title="Define Conditions"
                            )
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="conditions-cancel-button",
                                children=[
                                    "Cancel",
                                ],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="conditions-save-button",
                                children=[
                                    "Save",
                                ],
                                className="bg-primary text-secondary rounded-3",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
                size="lg",
            ),
            Frame(
                [
                    Dropdown(
                        [],
                        id="kpi-select",
                        className="text-dark h5",
                        clearable=False,
                    )
                ],
                title="Select KPI to simulate...",
                id="kpi-select-frame",
                style={"display": "none"},
            ),
            html.Div(
                html.Div(
                    [
                        Button(
                            id="experiment-setup-save-button",
                            children=[
                                Spinner(
                                    id="experiment-setup-save-button-spinner",
                                    size="sm",
                                    spinner_style={"display": "none"},
                                ),
                                InputField(
                                    id="experiment-setup-save-button-timestamp",
                                    className="d-none",
                                ),
                                "Run",
                            ],
                            outline=True,
                            color="primary",
                            className="rounded-3 d-flex align-items-center gap-2",
                        ),
                    ],
                    className="d-flex justify-content-end py-3",
                ),
                style={"display": "none"},
                id="experiment-setup-save",
            ),
            html.Div(id="progress-bar-container"),
            dcc.Interval(
                id="progress-interval", n_intervals=0, interval=1000, disabled=True
            ),
            html.Div(id="toast-container"),
            dcc.Store(id="experiment-setup-store", data={}),
        ]
    )


@callback(
    Output("plant-config-select", "options"),
    Input("auth-store", "modified_timestamp"),
    State("auth-store", "data"),
    prevent_initial_call=True,
)
def get_existing_plant_configs_on_section_load(children, user_store):
    """Get user existing plant configurations and load into dropdown"""

    headers = User.get_headers_from_store(user_store)
    response, success = api.get(f"/plant-configurations", headers=headers)

    if not success:
        return no_update

    data = response["data"]
    options = [
        {
            "label": plant_config["name"],
            "value": plant_config["name"],
        }
        for plant_config in data
    ]

    return options


@callback(
    Output("experiment-setup-store", "data", allow_duplicate=True),
    Output("experiment-type-select-frame", "style", allow_duplicate=True),
    Output("kpi-select", "options", allow_duplicate=True),
    Output("kpi-select", "value", allow_duplicate=True),
    Output("experiment-type-select", "value", allow_duplicate=True),
    Output("experiment-setup-tables", "style", allow_duplicate=True),
    Output("kpi-select-frame", "style", allow_duplicate=True),
    Output("experiment-setup-save", "style", allow_duplicate=True),
    Output("experiment-result-section", "style", allow_duplicate=True),
    Input("plant-config-select", "value"),
    State("experiment-setup-store", "data"),
    State("auth-store", "data"),
    prevent_initial_call=True,
)
def get_plant_config_on_select(
    plant_config_name: str, store: Dict[str, Any], user_store
):
    """Get existing plant configuration details based on user selection
    and reveal experiment type selection"""

    print()
    print(f"-----------get_plant_config_on_select Callback----------")

    encoded_name = plant_config_name
    headers = User.get_headers_from_store(user_store)

    params = {"configName": encoded_name}

    # Get equipment type presets
    response, success = api.get(
        f"/presets/equipment-details", params=params, headers=headers
    )

    if not success:
        return (
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
        )

    data = response["data"]
    prerequsites_by_equipment_type = {
        t["type"]: {
            param["title"]: param["prerequisites"]
            for param in t["setpoints"] + t["conditions"]
        }
        for t in data
    }

    response, success = api.get(
        f"/presets/stream-types", params=params, headers=headers
    )

    if not success:
        return (
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
        )

    data = response["data"]
    prerequsites_by_connection_type = {
        t["type"]: {
            param["title"]: param["prerequisites"]
            for param in t["setpoints"] + t["conditions"]
        }
        for t in data
    }

    # Get existing plant config details
    response, success = api.get(
        f"/plant-configurations/{encoded_name}", headers=headers
    )

    if not success:
        return (
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
        )

    data = response["data"]
    materials = [row["name"] for row in data["materials"]]
    store["materials"] = materials
    store["connections"] = {}
    store["equipment"] = {
        eq["equipmentId"]: eq
        for eq in data["equipments"]
        if eq["equipmentType"] in EquipmentType.valid_equipment_types()
    }

    # Create unique name for stream and add to store where name is key and object is value
    for cn in data["connections"]:
        name = f"{cn['upstreamEquipment']}->{cn['downstreamEquipment']}"
        cn["name"] = name

        selection_set = set(selection["value"] for selection in cn["selections"])
        connection_type = cn["type"]

        filtered_setpoints, filtered_conditions = [], []
        for setpoint in cn["setpoints"]:
            title = setpoint["title"]
            prereq_set: Union[None, Set] = (
                None
                if prerequsites_by_connection_type[connection_type][title] is None
                else set(prerequsites_by_connection_type[connection_type][title])
            )
            if (
                (len(prereq_set & selection_set) != 0)
                if prereq_set
                else (prereq_set is not None)
            ):
                filtered_setpoints.append(setpoint)

        for condition in cn["conditions"]:
            title = condition["title"]
            prereq_set: Union[None, Set] = (
                None
                if prerequsites_by_connection_type[connection_type][title] is None
                else set(prerequsites_by_connection_type[connection_type][title])
            )
            if (
                (len(prereq_set & selection_set) != 0)
                if prereq_set
                else (prereq_set is not None)
            ):
                filtered_conditions.append(condition)

        cn["setpoints"] = filtered_setpoints
        cn["conditions"] = filtered_conditions
        store["connections"][name] = cn

    # For each equipment, based on the selections, keep only the setpoints and conditions whose prereqs are met
    for _, eq in store["equipment"].items():
        selection_set = set(selection["value"] for selection in eq["selections"])
        equipment_type = eq["equipmentType"]

        filtered_setpoints, filtered_conditions = [], []
        for setpoint in eq["setpoints"]:
            title = setpoint["title"]
            prereq_set: Union[None, Set] = (
                None
                if prerequsites_by_equipment_type[equipment_type][title] is None
                else set(prerequsites_by_equipment_type[equipment_type][title])
            )
            if (
                (len(prereq_set & selection_set) != 0)
                if prereq_set
                else (prereq_set is not None)
            ):
                filtered_setpoints.append(setpoint)

        for condition in eq["conditions"]:
            title = condition["title"]
            prereq_set: Union[None, Set] = (
                None
                if prerequsites_by_equipment_type[equipment_type][title] is None
                else set(prerequsites_by_equipment_type[equipment_type][title])
            )
            if (
                (len(prereq_set & selection_set) != 0)
                if prereq_set
                else (prereq_set is not None)
            ):
                filtered_conditions.append(condition)

        eq["setpoints"] = filtered_setpoints
        eq["conditions"] = filtered_conditions

    # Get existing plant config details
    response, success = api.get(
        f"/plant-configurations/{encoded_name}/kpis", headers=headers
    )

    if not success:
        return (
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
        )

    data = response["data"]
    store["kpis"] = {kpi["name"]: kpi for kpi in data}
    kpi_options = [kpi["name"] for kpi in data]

    return (
        store,
        {"display": "block"},
        kpi_options,
        None,
        None,
        {"display": "none"},
        {"display": "none"},
        {"display": "none"},
        {"display": "none"},
    )


@callback(
    Output(
        PaginatedTableAIO.ids.table("material-flow-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output(
        PaginatedTableAIO.ids.table("entity-settings-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output(
        PaginatedTableAIO.ids.table("material-flow-table"),
        "columnSize",
        allow_duplicate=True,
    ),  # Note: These are needed to make the columns autosized after appearing
    Output(
        PaginatedTableAIO.ids.table("entity-settings-table"),
        "columnSize",
        allow_duplicate=True,
    ),
    Output("experiment-setup-tables", "style"),
    Output("kpi-select-frame", "style"),
    Output("experiment-setup-save", "style"),
    Input("experiment-type-select", "value"),
    State("experiment-setup-store", "data"),
    prevent_initial_call=True,
)
def load_tables_on_experiment_type_selection(
    experiment_type: str, store: Dict[str, Any]
):
    """Load Material Flow and Entity Settings tables when user selects an experiment type"""

    print()
    print(f"-----------load_tables_on_experiment_type_selection Callback----------")

    # Experiment type is not used for any filtering for now
    if experiment_type is None:
        return (
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
        )

    # Create rows for Material Flow table
    material_flow_row_data = []
    for stream in store["connections"].values():
        # Only include input streams / lower battery limits
        if stream["type"] != StreamType.InputStream.value:
            continue

        stream_name = f"{stream['upstreamEquipment']}->{stream['downstreamEquipment']}"
        for material in store["materials"]:
            row = {
                "id": UUIDService.generate_id(),
                "stream_name": stream_name,
                "material": material,
                "composition": 0,
                "entity_type": stream["type"],
            }
            material_flow_row_data.append(row)

    print(f"material_flow_row_data1 = {material_flow_row_data}")

    # Create rows for Equipment Settings table
    entity_settings_row_data = []

    return (
        material_flow_row_data,
        entity_settings_row_data,
        "sizeToFit",
        "sizeToFit",
        {"display": "flex"},
        {"display": "block"},
        {"display": "block"},
    )


@callback(
    Output(PaginatedTableAIO.ids.table("material-flow-table"), "rowData"),
    Input(PaginatedTableAIO.ids.table("material-flow-table"), "cellValueChanged"),
    State(PaginatedTableAIO.ids.table("material-flow-table"), "rowData"),
    prevent_initial_call=True,
)
def material_flow_table_validation(cell_changed, row_data):
    """Input Validation for when user changes any cell in Material Flow table"""

    print()
    print(f"-----------material_flow_table_validation Callback----------")

    updated_row = cell_changed[0]["data"]

    # Check for empty value
    if updated_row["composition"] is None:
        updated_row["composition"] = 0

    # Set values to be within [0, 100]
    updated_row["composition"] = min(updated_row["composition"], 100)
    updated_row["composition"] = max(updated_row["composition"], 0)

    # Limit to 2 d.p.
    updated_row["composition"] = round(updated_row["composition"], 2)

    # Check for empty value
    if updated_row["composition"] is None:
        updated_row["composition"] = 0

    row_data = [
        updated_row if row["id"] == updated_row["id"] else row for row in row_data
    ]

    return row_data


@callback(
    Output(
        PaginatedTableAIO.ids.table("entity-settings-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Input("entity-settings-table-add-button", "n_clicks"),
    Input("entity-settings-table-delete-button", "n_clicks"),
    State(PaginatedTableAIO.ids.table("entity-settings-table"), "selectedRows"),
    State(PaginatedTableAIO.ids.table("entity-settings-table"), "rowData"),
    State("experiment-setup-store", "data"),
    prevent_initial_call=True,
)
def add_or_del_entity_setting(
    add_clicks, delete_clicks, selected_rows, row_data, store
):
    """Configure add and delete of rows for Equipment & Stream Settings table"""

    print()
    print(f"-----------add_or_del_entity_setting Callback----------")

    triggered_id = ctx.triggered_id

    available_equipment_list, available_connection_list = get_available_entity_lists(
        row_data, store
    )
    entity_list = available_equipment_list + available_connection_list

    if triggered_id == "entity-settings-table-add-button":
        # Do nothing if all entities have been added
        if not available_equipment_list and not available_connection_list:
            return no_update

        # Add equipment by default if available, else add connection
        if available_equipment_list:
            default_equipment = store["equipment"][available_equipment_list[0]]
            default_entity_name = default_equipment["equipmentId"]
            new_row = {
                "id": UUIDService.generate_id(),
                "name": default_entity_name,
                "name_options": [],
                "setpoints": {
                    data["title"]: {
                        "upper_value": get_upper_range(
                            data["bounds"][1], data["value"], BOUNDS_PERCENTAGE
                        ),
                        "lower_value": get_lower_range(
                            data["bounds"][0], data["value"], BOUNDS_PERCENTAGE
                        ),
                        "upper_bound": data["bounds"][1],
                        "lower_bound": data["bounds"][0],
                        "unit": data["unit"],
                    }
                    for data in default_equipment["setpoints"]
                },
                "conditions": {
                    data["title"]: {
                        "upper_value": get_upper_range(
                            data["bounds"][1], data["value"], BOUNDS_PERCENTAGE
                        ),
                        "lower_value": get_lower_range(
                            data["bounds"][0], data["value"], BOUNDS_PERCENTAGE
                        ),
                        "upper_bound": data["bounds"][1],
                        "lower_bound": data["bounds"][0],
                        "unit": data["unit"],
                    }
                    for data in default_equipment["conditions"]
                },
                "entity_super_type": EntityType.Equipment.value,
                "entity_type": default_equipment["equipmentType"],
            }
        else:
            default_connection = store["connections"][available_connection_list[0]]
            default_entity_name = default_connection["name"]
            new_row = {
                "id": UUIDService.generate_id(),
                "name": default_entity_name,
                "name_options": [],
                "setpoints": {
                    data["title"]: {
                        "upper_value": get_upper_range(
                            data["bounds"][1], data["value"], BOUNDS_PERCENTAGE
                        ),
                        "lower_value": get_lower_range(
                            data["bounds"][0], data["value"], BOUNDS_PERCENTAGE
                        ),
                        "upper_bound": data["bounds"][1],
                        "lower_bound": data["bounds"][0],
                        "unit": data["unit"],
                    }
                    for data in default_connection["setpoints"]
                },
                "conditions": {
                    data["title"]: {
                        "upper_value": get_upper_range(
                            data["bounds"][1], data["value"], BOUNDS_PERCENTAGE
                        ),
                        "lower_value": get_lower_range(
                            data["bounds"][0], data["value"], BOUNDS_PERCENTAGE
                        ),
                        "upper_bound": data["bounds"][1],
                        "lower_bound": data["bounds"][0],
                        "unit": data["unit"],
                    }
                    for data in default_connection["conditions"]
                },
                "entity_super_type": EntityType.Connection.value,
                "entity_type": default_connection["type"],
            }

        row_data.append(new_row)

        # Update entity dropdown options
        for row in row_data:
            row["name_options"] = [row["name"]] + [
                e for e in entity_list if e != default_entity_name
            ]

        return row_data

    elif triggered_id == "entity-settings-table-delete-button":
        selected_id = selected_rows[0]["id"]
        row_data = [row for row in row_data if row["id"] != selected_id]

        # Update entity dropdown options
        for row in row_data:
            row["name_options"] = [row["name"]] + entity_list

        return row_data

    return no_update


@callback(
    Output(
        PaginatedTableAIO.ids.table("entity-settings-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Input(PaginatedTableAIO.ids.table("entity-settings-table"), "cellValueChanged"),
    State("experiment-setup-store", "data"),
    State(PaginatedTableAIO.ids.table("entity-settings-table"), "rowData"),
    prevent_initial_call=True,
)
def update_entity_selection(
    cell_changed,
    store,
    row_data,
):
    """Update entity selection and dropdown options when user changes selects a different equipment or stream in Equipment & Stream Settings table"""
    print()
    print(f"-----------update_entity_selection Callback----------")

    updated_col = cell_changed[0]["colId"]
    row_index = cell_changed[0]["rowIndex"]
    new_entity_name = cell_changed[0]["data"]["name"]

    available_equipment_list, available_connection_list = get_available_entity_lists(
        row_data, store
    )
    entity_list = available_equipment_list + available_connection_list

    for row in row_data:
        row["name_options"] = [row["name"]] + entity_list

    modified_row = row_data[row_index]

    # Check if new entity selected is equipment or connection
    all_equipment_set = set(_ for _ in store["equipment"].keys())
    if new_entity_name in all_equipment_set:
        store_key = "equipment"
        modified_row["entity_super_type"] = EntityType.Equipment.value
        modified_row["entity_type"] = store[store_key][new_entity_name]["equipmentType"]
    else:
        store_key = "connections"
        modified_row["entity_super_type"] = EntityType.Connection.value
        modified_row["entity_type"] = store[store_key][new_entity_name]["type"]

    modified_row["setpoints"] = {
        data["title"]: {
            "upper_value": get_upper_range(
                data["bounds"][1], data["value"], BOUNDS_PERCENTAGE
            ),
            "lower_value": get_lower_range(
                data["bounds"][0], data["value"], BOUNDS_PERCENTAGE
            ),
            "upper_bound": data["bounds"][1],
            "lower_bound": data["bounds"][0],
            "unit": data["unit"],
        }
        for data in store[store_key][new_entity_name]["setpoints"]
    }

    modified_row["conditions"] = {
        data["title"]: {
            "upper_value": get_upper_range(
                data["bounds"][1], data["value"], BOUNDS_PERCENTAGE
            ),
            "lower_value": get_lower_range(
                data["bounds"][0], data["value"], BOUNDS_PERCENTAGE
            ),
            "upper_bound": data["bounds"][1],
            "lower_bound": data["bounds"][0],
            "unit": data["unit"],
        }
        for data in store[store_key][new_entity_name]["conditions"]
    }

    return row_data


@callback(
    Output("progress-bar-container", "children", allow_duplicate=True),
    Output("toast-container", "children"),
    Output("progress-interval", "disabled"),
    Output("experiment-result-section", "style"),
    Output("experiment-setup-save-button-spinner", "spinner_style"),
    Output("experiment-setup-save-button-timestamp", "value"),
    Input("experiment-setup-save-button", "n_clicks"),
    State(PaginatedTableAIO.ids.table("material-flow-table"), "rowData"),
    State(PaginatedTableAIO.ids.table("entity-settings-table"), "rowData"),
    State("toast-container", "children"),
    State("experiment-type-select", "value"),
    State("kpi-select", "value"),
    prevent_initial_call=True,
)
def save_experiment_setup(
    save_clicks,
    material_flow_row_data,
    entity_settings_row_data,
    toast_children,
    experiment_type,
    kpi,
):
    """Save experiment setup and run experiment"""

    toast_manager = ToastManager(toast_children)
    errors = []

    material_flow_df = pd.DataFrame(material_flow_row_data)

    # Validate that there is at least 1 Upper Battery Limit
    if material_flow_df.empty:
        errors.append("Please add at least 1 BatteryIn in your plant configuration.")
    else:
        # Validate that all streams have composition that add up to 100% in Material Flow table
        grouped_df = (
            material_flow_df.groupby("stream_name")[["composition"]].sum().reset_index()
        )
        filtered_df = grouped_df[grouped_df["composition"] != 100]
        invalid_material_flows = list(filtered_df["stream_name"])

        if invalid_material_flows:
            errors.append(
                "Please make sure that all materials are filled in and correct."
            )

    # Make sure user has selected at least 1 entity
    if not entity_settings_row_data:
        errors.append("Please fill at least 1 equipment or stream.")

    # For all entity settings check that lower range is not equal to lower range
    invalid_var_range_list = []
    for row in entity_settings_row_data:
        entity_name = row["name"]
        for title, sp in row["setpoints"].items():
            if sp["lower_value"] == sp["upper_value"]:
                invalid_var_range_list.append(f"{entity_name} - {title}")
        for title, sp in row["conditions"].items():
            if sp["lower_value"] == sp["upper_value"]:
                invalid_var_range_list.append(f"{entity_name} - {title}")
    if invalid_var_range_list:
        errors.append(
            f"Lower and upper range cannot be equal. Please check {', '.join(invalid_var_range_list)}"
        )

    # Make sure user has selected KPI
    if not kpi:
        errors.append("Please select a KPI.")

    # Display errors
    if errors:
        new_toast = toast_manager.make_toast(
            errors,
            category="warning",
        )
        toast_manager.add_toast(new_toast)

        return (
            no_update,
            toast_manager.toasts,
            no_update,
            {"display": "none"},
            no_update,
            no_update,
        )

    children = (
        ProgressBar(
            id="progress-bar",
            value=0,
            animated=True,
            style={"height": "5px"},
            color="primary",
        ),
    )

    return (
        children,
        no_update,
        False,
        {"display": "none"},
        {"display": "block"},
        datetime.now().isoformat(),
    )


@callback(Output("progress-bar", "value"), Input("progress-interval", "n_intervals"))
def update_progress(n):
    value = min(n * 100 / DEFAULT_SECONDS, 100)
    return value


@callback(
    Output("progress-bar-container", "children", allow_duplicate=True),
    Output("progress-interval", "disabled", allow_duplicate=True),
    Output("progress-interval", "n_intervals", allow_duplicate=True),
    Output("experiment-result-store", "data", allow_duplicate=True),
    Output("toast-container", "children", allow_duplicate=True),
    Output(
        "experiment-setup-save-button-spinner", "spinner_style", allow_duplicate=True
    ),
    Input("progress-interval", "disabled"),
    State("plant-config-select", "value"),
    State("experiment-setup-store", "data"),
    State(PaginatedTableAIO.ids.table("material-flow-table"), "rowData"),
    State(PaginatedTableAIO.ids.table("entity-settings-table"), "rowData"),
    State("experiment-type-select", "value"),
    State("kpi-select", "value"),
    State("toast-container", "children"),
    State("auth-store", "data"),
    prevent_initial_call=True,
    background=True,
    running=[
        (Output("experiment-setup-save-button", "disabled"), True, False),
    ],
)
def run_experiment(
    is_interval_disabled,
    plant_config_name: str,
    store,
    material_flow_row_data,
    entity_settings_row_data,
    experiment_type,
    kpi,
    toast_children,
    user_store,
):
    """Send data to BE to run experiment"""

    headers = User.get_headers_from_store(user_store)

    print("Experiment running...")
    toast_manager = ToastManager(toast_children)

    # Send user data to BE
    entity = {}
    for eq in entity_settings_row_data:
        setpoints = [
            {
                "name": title,
                "entity_name": eq["name"],
                "type": "Setpoint",
                "bounds": [values["lower_bound"], values["upper_bound"]],
                "ranges": [values["lower_value"], values["upper_value"]],
                "unit": values["unit"],
            }
            for title, values in eq["setpoints"].items()
        ]

        conditions = [
            {
                "name": title,
                "entity_name": eq["name"],
                "type": "Condition",
                "bounds": [values["lower_bound"], values["upper_bound"]],
                "ranges": [values["lower_value"], values["upper_value"]],
                "unit": values["unit"],
            }
            for title, values in eq["conditions"].items()
        ]

        new_entity = {
            "entity_id": eq["name"],
            "spec_variables": setpoints + conditions,
            "entity_type": eq["entity_type"],
        }
        entity[eq["name"]] = new_entity

    # Add material flow compositions as setpoints
    for mf in material_flow_row_data:
        stream_name = mf["stream_name"]
        composition_in_fraction = mf["composition"] / 100
        if composition_in_fraction <= 0:
            continue
        mf_setpoint = {
            "name": mf["material"],
            "entity_name": stream_name,
            "type": "Setpoint",
            "bounds": [0, 1],
            "ranges": [composition_in_fraction, composition_in_fraction],
            "unit": "",
        }
        # Stream setting exists
        if entity.get(stream_name) is None:
            entity[stream_name] = {
                "entity_id": stream_name,
                "spec_variables": [mf_setpoint],
                "entity_type": mf["entity_type"],
            }
        else:
            entity[stream_name]["spec_variables"].append(mf_setpoint)

    # Send data to BE
    data = {
        "title": experiment_type,
        "config_name": plant_config_name,
        "entity_settings": {"value": list(entity.values())},
        "sim_kpi": {
            "name": kpi,
            "value": store["kpis"][kpi]["expression"],
        },
    }
    print("---------------------------------------")
    print("ExperimentConfig sent to BE = ")
    print(data, flush=True)
    print("---------------------------------------")

    response, success = api.post(f"/experiment", data=data, headers=headers)
    # TODO: [REMOVE] Temporary mock response
    # with open(
    #     "frontend/sections/experiment_setup_section/mock_experiment_results.json"
    # ) as f:
    #     response = json.load(f)
    #     success = True

    if not success:
        new_toast = toast_manager.make_toast(
            ["Failed to run experiment. Please try again later."], category="warning"
        )
        toast_manager.add_toast(new_toast)
        return [], True, 0, no_update, toast_manager.toasts, {"display": "none"}

    print("---------------------------------------")
    print("ExperimentResponse received from BE = ")
    print(response, flush=True)
    print("---------------------------------------")

    data = response["data"]["data"]

    # Transform data into format desired
    simulated_summary, entity_variable_mapping = {}, defaultdict(set)
    for scenario in data["simulated_summary"]["simulated_data"]:
        simulated_summary[scenario["scenario"]] = []

        for entity in scenario["entity_specification"]:
            for var in entity["variables"]:
                entity_variable_mapping[entity["entity"]].add(
                    generate_var_label(entity["entity"], var["name"])
                )
                simulated_summary[scenario["scenario"]].append(
                    {
                        "entity": entity["entity"],
                        "variable": generate_var_label(entity["entity"], var["name"]),
                        "type": var["type"],
                        "value": var["value"],
                        "unit": var["unit"],
                        "kpi_value": scenario["kpi_value"],
                        "scenario": scenario["scenario"],
                    }
                )
    entity_variable_mapping = {k: list(v) for k, v in entity_variable_mapping.items()}

    data["simulated_summary"] = simulated_summary
    data["entity_variable_mapping"] = entity_variable_mapping
    data["timestamp"] = datetime.now()

    print("experiment ended")
    return [], True, 0, data, toast_manager.toasts, {"display": "none"}


def generate_var_label(entity: str, var: str) -> str:
    return f"{entity} - {var}"


#######################################################################################################
@callback(
    Output("setpoints-modal", "is_open", allow_duplicate=True),
    Output(
        PaginatedTableAIO.ids.table("setpoints-table"), "rowData", allow_duplicate=True
    ),
    Output("conditions-modal", "is_open", allow_duplicate=True),
    Output(
        PaginatedTableAIO.ids.table("conditions-table"), "rowData", allow_duplicate=True
    ),
    Input(PaginatedTableAIO.ids.table("entity-settings-table"), "cellRendererData"),
    State("experiment-setup-store", "data"),
    State(PaginatedTableAIO.ids.table("entity-settings-table"), "rowData"),
    prevent_initial_call=True,
)
def open_setpoints_or_conditions_modal(row, store, entity_settings_row_data):
    """Opens Setpoints modal or Conditions modal"""

    print()
    print(f"-----------open_setpoints_or_conditions_modal Callback----------")

    print(row)
    col = row["colId"]
    row_index = row["rowIndex"]
    entity_settings_row = entity_settings_row_data[row_index]

    if col == "setpoints":
        setpoints_row_data = [
            {
                "id": UUIDService.generate_id(),
                "name": title,
                "lower_bound": sp["lower_bound"],
                "lower_value": sp["lower_value"],
                "upper_value": sp["upper_value"],
                "upper_bound": sp["upper_bound"],
                "unit": sp["unit"],
                "row_index": row_index,
            }
            for title, sp in entity_settings_row["setpoints"].items()
        ]
        return True, setpoints_row_data, no_update, no_update

    elif col == "conditions":
        conditions_row_data = [
            {
                "id": UUIDService.generate_id(),
                "name": title,
                "lower_bound": cd["lower_bound"],
                "lower_value": cd["lower_value"],
                "upper_value": cd["upper_value"],
                "upper_bound": cd["upper_bound"],
                "unit": cd["unit"],
                "row_index": row_index,
            }
            for title, cd in entity_settings_row["conditions"].items()
        ]
        return no_update, no_update, True, conditions_row_data


@callback(
    Output("setpoints-modal", "is_open", allow_duplicate=True),
    Input("setpoints-cancel-button", "n_clicks"),
    prevent_initial_call=True,
)
def close_setpoints_modal(n_clicks):
    """Close Setpoints modal"""
    if n_clicks:
        return False
    return no_update


@callback(
    Output("conditions-modal", "is_open", allow_duplicate=True),
    Input("conditions-cancel-button", "n_clicks"),
    prevent_initial_call=True,
)
def close_conditions_modal(n_clicks):
    """Close Conditions modal"""
    if n_clicks:
        return False
    return no_update


@callback(
    Output(PaginatedTableAIO.ids.table("setpoints-table"), "rowData"),
    Input(PaginatedTableAIO.ids.table("setpoints-table"), "cellValueChanged"),
    State(PaginatedTableAIO.ids.table("setpoints-table"), "rowData"),
    prevent_initial_call=True,
)
def setpoints_table_validation(cell_changed, row_data):
    return setpoints_and_conditions_table_validation(cell_changed, row_data)


@callback(
    Output(PaginatedTableAIO.ids.table("conditions-table"), "rowData"),
    Input(PaginatedTableAIO.ids.table("conditions-table"), "cellValueChanged"),
    State(PaginatedTableAIO.ids.table("conditions-table"), "rowData"),
    prevent_initial_call=True,
)
def conditions_table_validation(cell_changed, row_data):
    return setpoints_and_conditions_table_validation(cell_changed, row_data)


def setpoints_and_conditions_table_validation(cell_changed, row_data):
    """Input Validation for when user changes any cell in Setpoints or Conditions table"""

    row_index = cell_changed[0]["rowIndex"]
    updated_row = cell_changed[0]["data"]

    # Set lower value to be within bounds
    updated_row["lower_value"] = min(
        updated_row["lower_value"],
        updated_row["upper_bound"],
        updated_row["upper_value"],
    )
    updated_row["lower_value"] = max(
        updated_row["lower_value"], updated_row["lower_bound"]
    )

    # Limit to 3 d.p.
    updated_row["lower_value"] = round(updated_row["lower_value"], VARIABLE_PRECISION)

    # Set upper value to be within bounds
    updated_row["upper_value"] = min(
        updated_row["upper_value"], updated_row["upper_bound"]
    )
    updated_row["upper_value"] = max(
        updated_row["upper_value"],
        updated_row["lower_bound"],
        updated_row["lower_value"],
    )

    # Limit to 3 d.p.
    updated_row["upper_value"] = round(updated_row["upper_value"], VARIABLE_PRECISION)

    row_data[row_index] = updated_row

    return row_data


@callback(
    Output(
        PaginatedTableAIO.ids.table("entity-settings-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("setpoints-modal", "is_open", allow_duplicate=True),
    Input("setpoints-save-button", "n_clicks"),
    State(PaginatedTableAIO.ids.table("entity-settings-table"), "rowData"),
    State(PaginatedTableAIO.ids.table("setpoints-table"), "rowData"),
    prevent_initial_call=True,
)
def save_setpoints(save_clicks, entity_settings_row_data, row_data):
    return save_setpoints_or_conditions(
        "setpoints", save_clicks, entity_settings_row_data, row_data
    )


@callback(
    Output(
        PaginatedTableAIO.ids.table("entity-settings-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("conditions-modal", "is_open", allow_duplicate=True),
    Input("conditions-save-button", "n_clicks"),
    State(PaginatedTableAIO.ids.table("entity-settings-table"), "rowData"),
    State(PaginatedTableAIO.ids.table("conditions-table"), "rowData"),
    prevent_initial_call=True,
)
def save_conditions(save_clicks, entity_settings_row_data, row_data):
    return save_setpoints_or_conditions(
        "conditions", save_clicks, entity_settings_row_data, row_data
    )


def save_setpoints_or_conditions(
    parameter, save_clicks, entity_settings_row_data, row_data
):
    if not row_data:
        return no_update, True

    entity_settings_row_index = row_data[0]["row_index"]
    entity_settings_row = entity_settings_row_data[entity_settings_row_index]

    entity_settings_row[parameter] = {
        row["name"]: {
            "lower_bound": row["lower_bound"],
            "lower_value": row["lower_value"],
            "upper_value": row["upper_value"],
            "upper_bound": row["upper_bound"],
            "unit": row["unit"],
        }
        for row in row_data
    }

    return entity_settings_row_data, False


def get_available_entity_lists(
    row_data: List[Dict[str, Any]], store: Dict[str, Any]
) -> Tuple[List[str], List[str]]:
    """Given Equipment & Stream Setting row data and store, return a list of available equipment and stream names"""

    all_equipment_set = set(_ for _ in store["equipment"].keys())
    all_connection_set = set(
        c["name"]
        for c in store["connections"].values()
        if c["type"] == StreamType.InputStream.value
    )

    # Get available equipment and connections in form of equipment/connection names
    selected_entity_set = set(row["name"] for row in row_data)
    available_equipment_list = list(all_equipment_set - selected_entity_set)
    available_connection_list = list(all_connection_set - selected_entity_set)

    return available_equipment_list, available_connection_list


def get_upper_range(upper_bound: float, value: float, percentage: float) -> float:
    return min(upper_bound, value * (1 + (percentage / 100)))


def get_lower_range(lower_bound: float, value: float, percentage: float) -> float:
    return max(lower_bound, value * (1 - (percentage / 100)))
