from ._imports import *
import backend.infrastructure._db.repo as repo
import backend.infrastructure.runners.surrogate_trainers.runner_local as local_runner


class LocalSurrogate(core.ISurrogate):
    """
    Local implementation of ISurrogate interface that uses local training execution.

    This adapter provides a complete local implementation of the surrogate training
    system, using filesystem-based model registry and local training execution.
    It follows hexagonal architecture principles by injecting concrete implementations
    of the required ports.
    """

    def __init__(
        self,
    ):
        """
        Initialize the local surrogate implementation with local components.

        This constructor sets up the complete dependency injection for local execution:
        - SurrogateFilesystemRegistry for model/job persistence
        - LocalRunner for training execution
        """
        # Create local training runner instance
        training_runner = local_runner.SurrogateTrainingLocalRunner()

        # Pass component instances directly to parent constructor
        super().__init__(
            model_repo=repo.SurrogateFolderRepo(),
            training_runner=training_runner
        )
