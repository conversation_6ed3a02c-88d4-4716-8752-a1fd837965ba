from __future__ import annotations

from backend.core._atlas.valueobjects import VOSensor
from ._imports import *

#############################

# HELPERS


def _process_stream_and_eqpt_collections(
    entity: Union[core.at.ENTBaseEquipment, core.at.ENTBaseStream],
    atlas: core.at.AtlasRoot,
) -> Tuple[
    List[PlantConfig_DiscreteVar],
    List[PlantConfig_ContVar],
    List[PlantConfig_ContVar],
]:
    """Process collections into a tuple of selections, setpoints and conditions pydantic objects"""
    selections: List[PlantConfig_DiscreteVar] = []
    setpoints: List[PlantConfig_ContVar] = []
    conditions: List[PlantConfig_ContVar] = []

    # Filter streams
    if isinstance(entity, core.at.ENTBaseStream) and not isinstance(
        entity, core.at.InputStream
    ):
        return ([], [], [])

    for collection in entity.get_collections():

        # DiscreteVars
        if isinstance(collection, core.at.VarCollectionDiscreteSet):
            # Create a sorted set of variables, sorted by var.preqreuisites_for_selection.
            # _prerequi is either NONE or a Tuple. All tuples should be at end of list
            vars = [var for var in collection.items]
            vars_sorted = sorted(
                vars, key=lambda x: bool(x._prerequisites_for_selection)
            )

            for var in vars_sorted:
                assert isinstance(
                    var, core.at.VODiscreteVariable
                ), f"{var}:{type(var)} - what should happen here?"
                selections.append(PlantConfig_DiscreteVar.hydrate_schema(var, atlas))

        # SETPOINTS & CONDITIONS
        elif isinstance(collection, (core.at.VarCollectionContinuous)):
            for var in collection.items:
                assert isinstance(var, core.at.VOContinuousVariable)
                category = var.category
                schema = PlantConfig_ContVar.hydrate_schema(var, atlas)

                if category == core.at.VariableCategoryEnum.SETPOINT:
                    setpoints.append(schema)

                elif category == core.at.VariableCategoryEnum.EQUIPMENT_CONDITION:
                    conditions.append(schema)

                else:
                    logging.warning(
                        f"{var} not passed to FE because variable category == '{category}'. Validate with domain expert if this is expected"
                    )

        # COMPOUNDMASS
        elif isinstance(collection, core.at.VarCollectionCompoundMassRatio):

            # Skipping this so its not set
            pass

            # for var in collection.items:
            #     assert isinstance(var, core.at.VOCompoundMassRatio)
            #     category = var.category
            #     schema = PlantConfig_ContVar.hydrate_schema(var, atlas)

            #     if category == core.at.VariableCategoryEnum.SETPOINT:
            #         setpoints.append(schema)

            #     elif category == core.at.VariableCategoryEnum.EQUIPMENT_CONDITION:
            #         conditions.append(schema)

            #     else:
            #         logging.warning(
            #             f"{var} not passed to FE because variable category == '{category}'. Validate with domain expert if this is expected."
            #         )

        else:
            raise TypeError(
                f"collection type not managed: `{collection}` / `{type(collection)}`"
            )

    return (selections, setpoints, conditions)


def _get_entity_reactions(entity):
    """
    Returns the UUIDs of the reactions associated to an entity
    """
    if not isinstance(entity, core.at.ConversionReactor):
        return None

    reactions = list(entity.reactionset)
    return [str(reaction.uid) for reaction in reactions or []]


def _handle_fe_to_discreteitem(
    ui_label: str, ref_collection: core.at.VariableUIDCollection
) -> core.at.DiscreteItemSpecEnum:

    res = None
    labels = []

    # Search for matching enum
    for enum in core.at.DiscreteItemSpecEnum:

        enum_label = enum.stringify  # Get raw label
        transformed = ref_collection._transform_base_label(
            enum_label
        )  # Use same transform
        labels.append(transformed)

        logging.debug(f"Checking {enum} -> {transformed} == {ui_label}")

        if transformed == ui_label:
            logging.info(f"Found match: {enum}")
            res = enum
            break

    # No match found
    if res is None:
        raise KeyError(f"No discrete item found for UI label: {ui_label} vs {labels}")
    return res


def _handle_fe_to_variableVO(
    ui_label: str, atlas: core.at.AtlasRoot
) -> Union[core.at.VOBaseVariable, core.at.VOCompound]:

    # Try Variable
    try:
        return atlas.variables_collection.get_item(ui_label)
    except KeyError as e:
        logging.debug(
            f"`{ui_label}` not found in variables collection. Checking others"
        )
    except Exception as e:
        # unknown error
        logging.warning(f"unknown error: {e}")

    # Try Compound
    try:
        return atlas.compounds_collection.get_item(ui_label)
    except KeyError as e:
        logging.debug(
            f"`{ui_label}` not found in variables collection. Checking others"
        )
    except Exception as e:
        # unknown error
        logging.warning(f"unknown error: {e}")

    raise TypeError(
        f"{ui_label} not found in variable or compound collections. Investigate"
    )


def _handle_fe_to_vocompound(
    label: str, ref_collection: core.at.CompoundUIDCollection
) -> core.at.VOCompound:
    key = ref_collection.get_uid(label)
    item = ref_collection.get_item(key)

    return item


def _handle_discreteitem_to_fe(item: core.at.VODiscreteVariable) -> str:
    selection = item.value
    if selection is None:
        return ""
    assert isinstance(
        selection, core.at.DiscreteItemSpecEnum
    ), f"{selection} is wrong type"
    label = core.at.VariableUIDCollection._transform_base_label(selection.stringify)
    return label


def _handle_compound_to_fe(
    compound: core.at.VOCompound, ref_collection: core.at.CompoundUIDCollection
) -> str:
    return ref_collection.get_ui_label(compound)


def _handle_var_label_to_fe(
    var: core.at.VOBaseVariable, collection: core.at.VariableUIDCollection
) -> str:
    return collection.get_ui_label(var)


def _handle_var_val_to_fe(
    var: core.at.VOBaseVariable, atlas: core.at.AtlasRoot
) -> Union[float, int, str, None]:
    """converts a variable value to a FE return object"""
    value = var.value

    # Case 1: it a float
    if isinstance(value, (float, int)):
        return value

    # Case 2: its a compound
    elif isinstance(value, core.at.VOCompound):
        return core.at.CompoundUIDCollection.get_ui_label(value)

    # Case 3: its a discrete item enuget_ui_labelm
    elif isinstance(var, core.at.VODiscreteVariable):
        return _handle_discreteitem_to_fe(var)

    # Case 4: its a None
    elif value == None:
        return None

    # Case 5: its a iterable
    elif isinstance(value, (list, tuple)):
        logging.warning(f"{var} - {value} should be atomic.")
        return value[0]

    else:
        raise TypeError(f"`{var}`:`{value}` not accounted for. Check function.")


def _handle_var_discbounds_to_fe(
    var: core.at.VODiscreteVariable, atlas: core.at.AtlasRoot
):

    # Case 1: no bounds
    bounds = var.bounds
    if bounds is None:
        raise AttributeError(
            f"`{var}` bounds is incorrectly set: `{var.bounds}`. Check class vars."
        )

    bounds = list(bounds)
    sample = bounds[0]
    # Case 2: compound bounds
    if isinstance(sample, core.at.VOCompound):
        compound_collection = atlas.compounds_collection
        return [
            compound_collection.get_ui_label(compound)
            for compound in bounds
            if isinstance(compound, core.at.VOCompound)
        ]
    # Case 3: discrete item boudns
    return [
        atlas.variables_collection._transform_base_label(item.stringify)
        for item in bounds
        if isinstance(item, core.at.DiscreteItemSpecEnum)
    ]


def _handle_contvar_prereq_to_fe(
    var: core.at.VOContinuousVariable, Entity: Type[core.at.ENTBase]
) -> Optional[List[str]]:
    """Get prerequisites for making a continuous variable independent

    Finds all discrete vars which toggle the var to independent.
    Excludes self-referential prerequisites.
    """
    cont_var_enum = var.variable_enum
    prerequisites = set()

    n_toggle_dep: int = 0
    n_toggle_indep: int = 0

    for disc_var in Entity.DISCRETESET_DEFAULTS:
        # Get valid discrete items that aren't self-referential

        for discrete_item in disc_var.bounds or []:

            # Skip for VOCompound, etc
            if not isinstance(discrete_item, core.at.DiscreteItemSpecEnum):
                continue

            # Check for toggle dep
            if cont_var_enum in discrete_item.get_specification().toggle_dep:
                n_toggle_dep += 1

            # Check if toggles to independent and isn't self
            if (
                cont_var_enum
                in discrete_item.get_specification().toggle_indep
                # and discrete_item.stringify != cont_var_enum.stringify
            ):

                base_label = discrete_item.stringify
                prerequisites.add(
                    core.at.VariableUIDCollection._transform_base_label(base_label)
                )
                n_toggle_indep += 1

    prerequisites = list(prerequisites)

    # It CAN be toggled back to indep, therefore assume its negation is managed
    if len(prerequisites) > 0:
        return prerequisites

    # It is never toggled dependent, therefore assumes its always Independent
    if n_toggle_dep == 0:
        return []

    # There is a case where it is toggled to dep, but no case where its toggled back. Assume it is always dep
    # NOTE: the better case would be to check within each discrete var.
    if n_toggle_dep > 0:
        return None

    else:
        raise ValueError(
            f"There is a missing case for `{Entity}.{var}`. Review function: \nn_toggle_dep: `{n_toggle_dep}`\nn_toggle_indep: `{n_toggle_indep}`\nprerequisites: `{prerequisites}`"
        )


#############################

# PLANT CONFIG


class PlantConfig_DiscreteVar(BaseModel):
    """These are discrete variables"""

    title: str = Field(..., examples=["calculation_mode"])
    value: Optional[str] = Field(..., examples=["TempAndPressure"])
    uuid_str: Optional[str] = Field(None, examples=["234sdf2sdfagsdf"])

    @classmethod
    def hydrate_schema(
        cls, var: core.at.VODiscreteVariable, atlas: core.at.AtlasRoot
    ) -> PlantConfig_DiscreteVar:
        TITLE = _handle_var_label_to_fe(var, atlas.variables_collection)
        VALUE = _handle_var_val_to_fe(var, atlas)
        if not isinstance(VALUE, str) or VALUE is None:
            raise ValueError(f"{var} - value: `{VALUE}`... should be a string")
        UUID_STR = str(var.uid)

        return PlantConfig_DiscreteVar(title=TITLE, value=VALUE, uuid_str=UUID_STR)

    def bootstrap_atlas(
        self,
        entity: Union[core.at.ENTBaseEquipment, core.at.ENTBaseStream],
        atlas: core.at.AtlasRoot,
    ) -> None:
        """Update entities discrete var value"""
        variable = self._get_variable(entity)
        value = self._convert_value(atlas) if self.value else None

        if value is None:
            return
        entity.set_value(variable.variable_enum, value)

    def _get_variable(self, entity: core.at.ENTBase) -> core.at.VODiscreteVariable:
        """Get Discrete variable by title"""
        var = next(
            (
                var
                for var in entity.get_variables()
                if core.at.VariableUIDCollection.get_ui_label(var) == self.title
            ),
            None,
        )

        if not var or not isinstance(var, core.at.VODiscreteVariable):
            raise ValueError(
                f"Discrete Variable `{self.title}` not found in `{entity}`"
            )

        return var

    def _convert_value(
        self, atlas: core.at.AtlasRoot
    ) -> Union[core.at.DiscreteItemSpecEnum, core.at.VOCompound, None]:
        """Convert FE val to domain obj"""

        # Defensive
        if not self.value or self.value.strip() == "":
            return None

        # Try Enum
        try:
            return _handle_fe_to_discreteitem(self.value, atlas.variables_collection)
        except KeyError:
            logging.debug(f"Value '{self.value}' not a discrete item, trying compound")
        except Exception as e:
            logging.error(f"unknown error: {e}")

        # Try Compound Lookup
        try:
            return _handle_fe_to_vocompound(self.value, atlas.compounds_collection)
        except KeyError:
            logging.debug(f"Value '{self.value}' not a compound")
        except Exception as e:
            logging.error(f"unknown error: {e}")

        # Neither worked
        raise ValueError(
            f"Cannot convert '{self.value}' - not a valid discrete item or compound"
        )


class PlantConfig_ContVar(BaseModel):
    title: str
    value: Union[float, Iterable, None]
    unit: str
    bounds: Tuple[Optional[float], Optional[float]]
    uuid_str: Optional[str] = Field(None, examples=["234sdf2sdfagsdf"])

    @classmethod
    def hydrate_schema(
        cls,
        var: Union[core.at.VOContinuousVariable, core.at.VOCompoundMassRatio],
        atlas: core.at.AtlasRoot,
    ) -> PlantConfig_ContVar:
        UUID_STR = str(var.uid)
        if isinstance(var, core.at.VOContinuousVariable):
            title = _handle_var_label_to_fe(var, atlas.variables_collection)
            value = _handle_var_val_to_fe(var, atlas)
            unit = var.unit
            bounds = var.bounds
            if bounds is None:
                raise ValueError(f"Bounds required for {var}")

            return PlantConfig_ContVar(title=title, value=value, unit=unit, bounds=bounds, uuid_str=UUID_STR)  # type: ignore

        elif isinstance(var, core.at.VOCompoundMassRatio):
            compound = var.compound
            title = _handle_compound_to_fe(compound, atlas.compounds_collection)
            value = _handle_var_val_to_fe(var, atlas)
            unit = var.unit
            bounds = var.bounds
            assert (
                bounds is not None
            ), f"{var} - bounds is {bounds}. It should have a value"
            return PlantConfig_ContVar(title=title, value=value, unit=unit, bounds=bounds, uuid_str=UUID_STR)  # type: ignore

        else:
            raise TypeError(f"{type(var)} not accounted for")

    def bootstrap_atlas(self, entity: core.at.ENTBase, atlas: core.at.AtlasRoot):
        """Given an entity, sets its value and bounds for a variable (routed by title)"""

        var = _handle_fe_to_variableVO(self.title, atlas)

        # Set for CompoundMassRatio
        if isinstance(var, core.at.VOCompound):
            var_key = var
            logging.info(entity)
            entity.set_value(var_key, self.value)

        # Set for ContVar
        elif isinstance(var, core.at.VOContinuousVariable):
            var_key = var.variable_enum
            entity.set_bounds(var_key, self.bounds)
            entity.set_value(var_key, self.value)

        else:
            raise TypeError(f"{var} is not accounted for")

        # if var_key == core.at.ContVarSpecEnum.DummyCompound:
        #     print(var, var_key)


class PlantConfigEquipment(BaseModel):
    equipmentId: str = Field(..., examples=["HT-001"])
    equipmentType: str = Field(..., examples=["Heater"])
    selections: List[PlantConfig_DiscreteVar]
    setpoints: List[PlantConfig_ContVar]
    conditions: List[PlantConfig_ContVar]
    reactions: Optional[List[str]] = Field(  # Requied only for reactors
        default=None,
        description="List of PC_Reaction UIDs",
        examples=["UUID@#$@#$, UUID2342343, ..."],
    )

    @classmethod
    def hydrate_schema(
        cls, entity: core.at.ENTBaseEquipment, atlas: core.at.AtlasRoot
    ) -> PlantConfigEquipment:
        """Convert domain entity to API schema"""
        collections = _process_stream_and_eqpt_collections(entity, atlas)
        reactions = _get_entity_reactions(entity)

        return cls(
            equipmentId=entity.label,
            equipmentType=entity.entity_type,
            selections=collections[0],
            setpoints=collections[1],
            conditions=collections[2],
            reactions=reactions,
        )

    def bootstrap_atlas(self, atlas: core.at.AtlasRoot) -> core.at.ENTBaseEquipment:
        """Convert API schema to domain entity"""
        entity = self._handle_equipment(atlas)
        self._apply_variables(entity, atlas)
        self._apply_reactions(entity, atlas)
        return entity

    def _handle_equipment(self, atlas: core.at.AtlasRoot) -> core.at.ENTBaseEquipment:
        """Handle equipment creation/retrieval"""
        if self.equipmentId in atlas.equipments:
            return atlas.get_equipment(by_label=self.equipmentId)
        else:
            return atlas.add_equipment(
                core.at.EQP_STRATEGY[self.equipmentType], self.equipmentId
            )

    def _apply_variables(
        self, entity: core.at.ENTBaseEquipment, atlas: core.at.AtlasRoot
    ) -> None:
        """Apply variables to entity. Note: this does NOT save vars in "NONE" category."""

        # Discretevars
        for var in self.selections:
            var.bootstrap_atlas(entity, atlas)

        # Contvars
        for var in self.setpoints + self.conditions:
            var.bootstrap_atlas(entity, atlas)

    def _apply_reactions(
        self, entity: core.at.ENTBaseEquipment, atlas: core.at.AtlasRoot
    ) -> None:
        """Apply reactions if entity is reactor"""
        if not isinstance(entity, core.at.ConversionReactor) or not self.reactions:
            return
        for reaction_uid in self.reactions:
            reaction = atlas.reaction_collection.get_item(uuid.UUID(reaction_uid))
            atlas.add_reaction_to_reactor(entity, reaction)


class PlantConfigConnection(BaseModel):
    upstreamEquipment: str = Field(..., examples=["HX-03"])
    downstreamEquipment: str = Field(..., examples=["HT-001"])
    type: str = Field(..., examples=["MaterialStream"])
    selections: List[PlantConfig_DiscreteVar] = Field(default_factory=list)
    setpoints: List[PlantConfig_ContVar] = Field(default_factory=list)
    conditions: List[PlantConfig_ContVar] = Field(default_factory=list)

    # TODO delete
    @classmethod
    def hydrate_schema(
        cls, entity: core.at.ENTBaseStream, atlas: core.at.AtlasRoot
    ) -> PlantConfigConnection:

        collections = _process_stream_and_eqpt_collections(entity, atlas)
        connections = atlas.get_stream_connections(entity.label)

        return cls(
            upstreamEquipment=connections[0],
            downstreamEquipment=connections[1],
            type=entity.entity_type,
            selections=collections[0],
            setpoints=collections[1],
            conditions=collections[2],
        )

    def bootstrap_atlas(self, atlas: core.at.AtlasRoot) -> core.at.ENTBaseStream:
        """Convert API schema to domain entity"""
        entity = self._handle_stream(atlas)
        self._apply_variables(entity, atlas)
        return entity

    def _handle_stream(self, atlas: core.at.AtlasRoot) -> core.at.ENTBaseStream:
        """Handle equipment creation/retrieval"""

        try:
            stream = atlas.get_stream(
                by_equipments=(self.upstreamEquipment, self.downstreamEquipment)
            )

        except KeyError as e:
            stream_constructor = core.at.STREAM_STRATEGY[self.type]
            stream = atlas.add_stream(
                self.upstreamEquipment,
                self.downstreamEquipment,
                stream_type=stream_constructor,
            )

        except Exception as e:
            # Unexpected error - log error and convert to runtime
            logging.error(f"System error handling stream: {str(e)}", exc_info=True)
            raise RuntimeError(f"Failed to handle stream: {str(e)}") from e

        return stream

    def _apply_variables(
        self, entity: core.at.ENTBaseStream, atlas: core.at.AtlasRoot
    ) -> None:
        """Apply variables to entity. Note: this does NOT save vars in "NONE" category."""

        # Discretevars
        for var in self.selections:
            var.bootstrap_atlas(entity, atlas)

        # Contvars
        for var in self.setpoints + self.conditions:
            var.bootstrap_atlas(entity, atlas)


class PlantConfigMaterial(BaseModel):
    name: str = Field(..., examples=["Benzene"])

    @classmethod
    def hydrate_schema(
        cls, compound: core.at.VOCompound, atlas: core.at.AtlasRoot
    ) -> PlantConfigMaterial:
        collection = atlas.compounds_collection

        return PlantConfigMaterial(name=_handle_compound_to_fe(compound, collection))

    def bootstrap_atlas(self) -> core.at.VOCompound:
        return core.at.CompoundRef.get_vocompound_from_label(self.name)


class PlantConfigReaction_Details(BaseModel):
    type: str = Field(..., examples=["Conversion"])
    stoichiometry: Dict[str, int] = Field(..., examples=[{"Benzene": -1, "Methane": 1}])
    base_compound: str = Field(..., examples=["Benzene"])
    # NOTE @ZL - added in new item
    # Varies depending on reaction type, e.g. reaction_conversion & reaction_phase for conversion rxn
    advanced_selections: Dict[str, Union[str, int, float]]

    @classmethod
    def hydrate_schema(
        cls, reaction: core.at.ConversionReaction, atlas: core.at.AtlasRoot
    ) -> PlantConfigReaction_Details:

        # Get all compounds in the reaction
        reaction_stoichiometry_items = reaction.get_collection(
            core.at.VarCollectionReactionStoich
        ).items

        stoichiometry = {
            _handle_compound_to_fe(vo_rs.compound, atlas.compounds_collection): int(
                vo_rs.value
            )
            for vo_rs in reaction_stoichiometry_items
            if isinstance(vo_rs, core.at.VOReactionStoich)
        }

        base_compound = _handle_compound_to_fe(
            reaction.get_value(core.at.DiscreteSetSpecEnum.BaseCompound),
            atlas.compounds_collection,
        )

        advanced_selections: Dict[str, str] = {
            "reaction_phase": reaction.get_value(
                core.at.DiscreteSetSpecEnum.ReactionPhase
            ).stringify,
            "conversion_expression": str(
                reaction.get_value(core.at.DiscreteSetSpecEnum.ConversionExpression)
            ),
        }

        return PlantConfigReaction_Details(
            type=reaction.__class__.__name__,
            stoichiometry=stoichiometry,
            base_compound=base_compound,
            advanced_selections=advanced_selections,  # type: ignore
        )


class PlantConfigReaction(BaseModel):
    id: str = Field(..., examples=["u123sdud312sa"])  # this is a uid
    name: str = Field(..., examples=["Benzene"])
    details: PlantConfigReaction_Details

    @classmethod
    def hydrate_schema(
        cls,
        reaction: core.at.ConversionReaction,
        atlas: core.at.AtlasRoot,
    ) -> PlantConfigReaction:
        details = PlantConfigReaction_Details.hydrate_schema(reaction, atlas)
        name = atlas.reaction_collection.get_ui_label(reaction)

        return PlantConfigReaction(id=str(reaction.uid), name=name, details=details)

    def bootstrap_atlas(
        self, atlas: core.at.AtlasRoot
    ) -> core.at.ConversionReaction:  # returns the reactionset ID and the reaction
        compound_collection = atlas.compounds_collection

        # Get vars
        base_compound = _handle_fe_to_vocompound(
            self.details.base_compound, compound_collection
        )

        comp_and_stoich = {
            _handle_fe_to_vocompound(k, compound_collection): v
            for k, v in self.details.stoichiometry.items()
        }

        reaction_phase = self.details.advanced_selections.get("reaction_phase")
        conversion_expression = self.details.advanced_selections.get(
            "conversion_expression"
        )

        # Defensive
        if reaction_phase is None or conversion_expression is None:
            raise AttributeError(
                "Reaction: Missing reaction phase or conversion expression"
            )

        # Create object
        reaction = core.at.ConversionReaction(self.name, uuid_str=self.id)

        # Add ReactionStoich
        for k, v in comp_and_stoich.items():
            reaction.add_variable(core.at.VOReactionStoich(k, v))

        # Update state
        reaction.set_value(core.at.DiscreteSetSpecEnum.BaseCompound, base_compound)
        reaction.set_value(
            core.at.DiscreteSetSpecEnum.ConversionExpression, str(conversion_expression)
        )
        reaction.set_value(
            core.at.DiscreteSetSpecEnum.ReactionPhase,
            core.at.DiscreteItemSpecEnum.from_stringify(reaction_phase),
        )

        # Register in Atlas
        atlas.register_reaction(reaction)

        return reaction


class EnumMap:
    """
    A simple namespace class to map between backend enums and frontend display strings
    """

    enum2str = {
        core.at.UserIndustryEnum.CHEMICAL: "Chemical",
        core.at.UserIndustryEnum.PHARMA: "Pharmaceuticals",
        core.at.MatrixEngineEnum.DWSIM: "DWSim",
        core.at.MatrixEngineEnum.GPROMS: "GRPROMS",
    }
    str2enum = {v: k for k, v in enum2str.items()}


# GET, POST /plant-configuration/{configName}
class PlantConfiguration(BaseModel):
    """Plant Configuration Schema for API interface"""

    user_industry: str
    matrix_engine: str
    is_template: bool = Field(...)
    equipments: List[PlantConfigEquipment]
    connections: List[PlantConfigConnection]
    materials: List[PlantConfigMaterial]
    reactions: List[PlantConfigReaction]

    @classmethod
    def hydrate_schema(
        cls, atlas: core.at.AtlasRoot, is_template: bool = False
    ) -> PlantConfiguration:
        """Creates API schema from domain model"""
        user_industry = atlas.user_config.user_industry
        user_industry = EnumMap.enum2str[user_industry]

        matrix_engine = atlas.user_config.matrix_engine
        matrix_engine = EnumMap.enum2str[matrix_engine]

        return cls(
            user_industry=user_industry,
            matrix_engine=matrix_engine,
            is_template=is_template,
            equipments=cls._hydrate_equipments(atlas),
            connections=cls._hydrate_connections(atlas),
            materials=cls._hydrate_materials(atlas),
            reactions=cls._hydrate_reactions(atlas),
        )

    @classmethod
    def _hydrate_equipments(
        cls, atlas: core.at.AtlasRoot
    ) -> List[PlantConfigEquipment]:
        """Maps equipment and streams to schema"""
        entities = [
            PlantConfigEquipment.hydrate_schema(
                entity=atlas.get_equipment(by_label=label), atlas=atlas  # type: ignore
            )
            for label in atlas.equipments
            if label is not None and atlas.get_equipment(by_label=label) is not None
        ]
        return entities

    @classmethod
    def _hydrate_connections(
        cls, atlas: core.at.AtlasRoot
    ) -> List[PlantConfigConnection]:
        entities = [
            PlantConfigConnection.hydrate_schema(
                entity=atlas.get_stream(by_label=label), atlas=atlas  # type: ignore
            )
            for label in atlas.streams
            if label is not None and atlas.get_stream(by_label=label) is not None
        ]
        return entities

    @classmethod
    def _hydrate_materials(cls, atlas: core.at.AtlasRoot) -> List[PlantConfigMaterial]:
        return [
            PlantConfigMaterial.hydrate_schema(compound, atlas)
            for compound in atlas.compounds_collection.items
        ]

    @classmethod
    def _hydrate_reactions(cls, atlas: core.at.AtlasRoot) -> List[PlantConfigReaction]:
        return [
            PlantConfigReaction.hydrate_schema(reaction, atlas)
            for reaction in atlas.reaction_collection.items
        ]

    @staticmethod
    def bootstrap_atlas(
        schema: PlantConfiguration,
        atlas_reference: Optional[core.at.AtlasRoot] = None,
        model_name: Optional[str] = None,
    ) -> core.at.AtlasRoot:
        """Create a new atlas object.
        If existing one is given, it will fork it and use it as base state.

        Raises:
            AttributeError for missing items
        """
        if atlas_reference is None and model_name is None:
            raise AttributeError("No model name or atlas reference provided.")

        # INITIALIZE ATLAS
        # TODO refactor plant id to be UUID driven
        plant_id = str(uuid.uuid4())
        user_industry = EnumMap.str2enum[schema.user_industry]
        matrix_engine = EnumMap.str2enum[schema.matrix_engine]
        user_config = core.at.UserAtlasConfig(
            user_industry=user_industry, matrix_engine=matrix_engine
        )

        atlas = (
            copy.deepcopy(atlas_reference)
            if atlas_reference is not None
            else core.at.AtlasRoot(model_name or "default", plant_id, user_config)
        )

        # CREATE IN REVERSE
        # COMPOUNDS
        target_compounds = [
            schema_obj.bootstrap_atlas() for schema_obj in schema.materials
        ]
        compound_collection = atlas.compounds_collection
        compound_collection.sync_collection(target_compounds)

        # REACTIONS
        reactions: List[core.at.ConversionReaction] = []
        for reaction_schema in schema.reactions:
            reactions.append(reaction_schema.bootstrap_atlas(atlas))
        atlas.reaction_collection.sync_collection(reactions)

        # EQUIPMENTS
        equipments = []
        existing_equipments = atlas.equipments
        revised_equipments = []

        # add or update
        for equipment_schema_object in schema.equipments:
            equipments.append(equipment_schema_object.bootstrap_atlas(atlas))
            revised_equipments.append(equipment_schema_object.equipmentId)
        # remove
        for eqp_id in set(existing_equipments) - set(revised_equipments):
            atlas.remove_equipment(eqp_id)

        # CONNECTIONS
        streams = []
        revised_streams = []
        existing_streams: List[Tuple[str, str]] = [(e[0], e[1]) for e in atlas.edgelist]

        for connection_schema_object in schema.connections:
            streams.append(connection_schema_object.bootstrap_atlas(atlas))
            revised_streams.append(
                (
                    connection_schema_object.upstreamEquipment,
                    connection_schema_object.downstreamEquipment,
                )
            )
        for stream_tuple in set(existing_streams) - set(revised_streams):
            atlas.remove_stream(by_equipment=stream_tuple)

        return atlas


class Preset_DiscreteVar(BaseModel):
    title: str = Field(..., examples=["Calculation Mode"])
    value: str = Field(..., examples=["Heat", "Pressure"])
    options: List[str] = Field(..., examples=[["Heat", "Pressure"]])
    prerequisites: List[str] = Field(
        ..., examples=[["Heat"]]
    )  # @ZL - amended to optional

    @classmethod
    def hydrate_schema(
        cls, var: core.at.VODiscreteVariable, atlas: core.at.AtlasRoot
    ) -> Preset_DiscreteVar:
        title = _handle_var_label_to_fe(var, atlas.variables_collection)
        value = _handle_var_val_to_fe(var, atlas)
        if value is None:
            value = ""
        options: List[str] = _handle_var_discbounds_to_fe(var, atlas)

        # Preqreuisites
        prerequisites = []
        if var.prerequisites_for_selection is not None:
            for discvar_set in var.prerequisites_for_selection:
                for item in list(discvar_set):
                    base_label = item.stringify
                    prerequisites.append(
                        core.at.VariableUIDCollection._transform_base_label(base_label)
                    )

        assert isinstance(value, str)

        return Preset_DiscreteVar(
            title=title, value=value, options=options, prerequisites=prerequisites
        )


class Preset_ContVar(BaseModel):
    title: str = Field(..., examples=["Outlet Temperature"])
    value: Union[float, int]
    unit: str = Field(..., examples=["K"])
    bounds: Tuple[Optional[float], Optional[float]] = Field(..., examples=[(50, 100)])
    prerequisites: Optional[List[str]] = Field(None, examples=[["Heat"]])

    @classmethod
    def hydrate_schema(
        cls,
        var: core.at.VOContinuousVariable,
        atlas: core.at.AtlasRoot,
        Entity: Type[core.at.ENTBase],
    ) -> Preset_ContVar:
        title = _handle_var_label_to_fe(var, atlas.variables_collection)
        value = _handle_var_val_to_fe(var, atlas)
        if value is None:
            value = 0.0
        unit = var.variable_enum.unit
        bounds = var.bounds
        prerequisites = _handle_contvar_prereq_to_fe(var, Entity)

        assert isinstance(value, (int, float))
        assert bounds is not None

        return Preset_ContVar(
            title=title,
            value=value,
            unit=unit,
            bounds=bounds,
            prerequisites=prerequisites,
        )


# GET /presets/equipment-details
class PresetEquipmentType(BaseModel):
    type: str = Field(..., examples=["Heater"])
    selections: List[Preset_DiscreteVar]
    setpoints: List[Preset_ContVar]
    conditions: List[Preset_ContVar]

    @classmethod
    def hydrate_schema(
        cls,
        Equipment: Type[core.at.ENTBaseEquipment],
        atlas: core.at.AtlasRoot,
    ) -> PresetEquipmentType:

        _type = Equipment.get_entity_type()

        SELECTIONS: List[Preset_DiscreteVar] = []
        vars = Equipment.DISCRETESET_DEFAULTS
        vars_sorted = sorted(vars, key=lambda x: bool(x._prerequisites_for_selection))
        for discrete_variable in vars_sorted:
            # DEFENSIVE: skip for some vars
            if discrete_variable.variable_enum in [
                core.at.DiscreteSetSpecEnum.Conversions,
                core.at.DiscreteSetSpecEnum.ComponentConversions,
            ]:
                continue

            SELECTIONS.append(
                Preset_DiscreteVar.hydrate_schema(discrete_variable, atlas)
            )

        SETPOINTS: List[Preset_ContVar] = []
        CONDITIONS: List[Preset_ContVar] = []
        for cont_var in list(Equipment.CONTVAR_DEFAULTS):
            # Get
            item = Preset_ContVar.hydrate_schema(cont_var, atlas, Equipment)

            # Route
            if cont_var.category == core.at.VariableCategoryEnum.SETPOINT:
                SETPOINTS.append(item)
            elif cont_var.category == core.at.VariableCategoryEnum.EQUIPMENT_CONDITION:
                CONDITIONS.append(item)
            else:
                logging.warning(
                    f"<PC_EquipmentType>: not including {cont_var} because of CATEGORY. is this intended?"
                )

        # SORT SELECTIONS, SETPOINTS AND CONDITIONS. ITEMS WITH PRE_REQ ARE LAST
        CONDITIONS = sorted(CONDITIONS, key=lambda x: len(x.prerequisites or []))
        SETPOINTS = sorted(SETPOINTS, key=lambda x: len(x.prerequisites or []))
        SELECTIONS = sorted(SELECTIONS, key=lambda x: len(x.prerequisites or []))

        return PresetEquipmentType(
            type=_type,
            selections=SELECTIONS,
            setpoints=SETPOINTS,
            conditions=CONDITIONS,
        )


# GET /presets/stream-types
# See docs for Root Model - https://docs.pydantic.dev/latest/concepts/models/#rootmodel-and-custom-root-types
class PresetStreamType(BaseModel):
    type: str = Field(..., examples=["InputStream"])
    selections: List[Preset_DiscreteVar] = Field(default=[])  # <- This is new
    setpoints: List[Preset_ContVar] = Field(default=[])  # <- This is new
    conditions: List[Preset_ContVar] = Field(default=[])  # <- This is new

    @classmethod
    def hydrate_schema(
        cls, Stream: Type[core.at.ENTBaseStream], atlas: core.at.AtlasRoot
    ) -> PresetStreamType:

        _type = Stream.get_entity_type()
        SELECTIONS: List[Preset_DiscreteVar] = []
        vars = Stream.DISCRETESET_DEFAULTS
        vars_sorted = sorted(vars, key=lambda x: bool(x._prerequisites_for_selection))
        for discrete_variable in vars_sorted:
            # DEFENSIVE: skip for some vars
            if discrete_variable.variable_enum in [
                core.at.DiscreteSetSpecEnum.Conversions,
                core.at.DiscreteSetSpecEnum.ComponentConversions,
            ]:
                continue

            SELECTIONS.append(
                Preset_DiscreteVar.hydrate_schema(discrete_variable, atlas)
            )

        SETPOINTS: List[Preset_ContVar] = []
        CONDITIONS: List[Preset_ContVar] = []
        for cont_var in list(Stream.CONTVAR_DEFAULTS):
            # Get
            item = Preset_ContVar.hydrate_schema(cont_var, atlas, Stream)

            # Route
            if cont_var.category == core.at.VariableCategoryEnum.SETPOINT:
                SETPOINTS.append(item)
            elif cont_var.category == core.at.VariableCategoryEnum.EQUIPMENT_CONDITION:
                CONDITIONS.append(item)
            else:
                logging.warning(
                    f"<PC_EquipmentType>: not including {cont_var} because of CATEGORY. is this intended?"
                )

        # SORT SELECTIONS, SETPOINTS AND CONDITIONS. ITEMS WITH PRE_REQ ARE LAST
        CONDITIONS = sorted(CONDITIONS, key=lambda x: len(x.prerequisites or []))
        SETPOINTS = sorted(SETPOINTS, key=lambda x: len(x.prerequisites or []))
        SELECTIONS = sorted(SELECTIONS, key=lambda x: len(x.prerequisites or []))

        return PresetStreamType(
            type=_type,
            selections=SELECTIONS,
            setpoints=SETPOINTS,
            conditions=CONDITIONS,
        )


# GET /presets/material-typis


class PresetMaterialType(BaseModel):
    name: str = Field(..., examples=["Methane"])
    cas_number: str = Field(..., examples=["123456-11-1"])


class PresetMaterialTypes(BaseModel):
    items: List[PresetMaterialType]


# GET /presets/sensor-types
class PresetSensorTypes(RootModel[List[str]]):
    pass


# METADATA


# GET, POST /plant-configurations
class PlantConfig_Metadata(BaseModel):
    name: str = Field(..., examples=["Plant Config 1"])
    createdDateTime: datetime
    editedDateTime: datetime
    is_template: bool


# Sensors
class SensorItem(BaseModel):
    label: str = Field(
        ...,
        description="Sensor name / csv column name",
        examples=["S-001", "temperature"],
    )
    variable_uid: uuid.UUID = Field(
        ...,
        description="UUID of variable associated with sensor",
        examples=["var-1-uuid", "var-2-uuid"],
    )

    @classmethod
    def hydrate_schema(cls, sensor: VOSensor) -> SensorItem:
        return SensorItem(label=sensor.label, variable_uid=sensor.variable_uid)

    @classmethod
    def bootstrap_obj(cls, label: str, variable_uid: uuid.UUID) -> VOSensor:
        return VOSensor(variable_uid=variable_uid, label=label)


class SensorMappingConfig(BaseModel):
    csvCol2varUUID: Dict[str, uuid.UUID] = Field(
        ...,
        description="Mapping between sensor / CSV column names and variable UUIDs",
        examples=[{"S-001": "123e4567-e89b-12d3-a456-************"}],
    )
    timestep_col: Optional[str] = Field(
        None,
        description="Column name that contains sequential identifiers (optional)",
        examples=["timestamp", "sequence_id"],
    )
    timeset_col: Optional[str] = Field(
        None,
        description="Column name that contains sequential identifiers (optional)",
        examples=["timeset", "Case No"],
    )

    @classmethod
    def hydrate_schema(cls, atlas: core.at.AtlasRoot) -> SensorMappingConfig:
        csvCol2varUUID = {}
        for sensor in atlas.sensor_collection.items:
            csvCol2varUUID[sensor.label] = sensor.variable_uid
        return SensorMappingConfig(
            csvCol2varUUID=csvCol2varUUID,
            timestep_col=atlas.sensor_timestep_col,
            timeset_col=atlas.sensor_timeset_col,
        )

    # @classmethod
    # def bootstrap_obj(
    #     cls, mapping: SensorMappingConfig, atlas: core.at.AtlasRoot
    # ) -> core.at.AtlasRoot:
    #     for label, var_uid in mapping.csvCol2varUUID.items():
    #         sensor = SensorItem.bootstrap_obj(label, var_uid)
    #         atlas.sensor_collection.add_item(sensor)

    #     return atlas


# GET /plant-configurations/{configName}/kpi-variables
class KPIVariableItem(BaseModel):
    variableName: str = Field(..., examples=["HX-001.temperature"])
    variableUnit: str = Field(..., examples=["K"])
    variableSensor: str = Field(..., examples=["S-0022"])
    variableEntity: str = Field(..., examples=["HX-001"])
    variableEntityType: str = Field(..., examples=["HX-001"])

    @classmethod
    def hydrate_schema(
        cls, variable: core.at.VOBaseVariable, kpi_label: str
    ) -> KPIVariableItem:

        return KPIVariableItem(
            variableName=kpi_label,
            variableUnit=variable.unit,
            variableSensor="NA",
            variableEntity=variable.parent.label,
            variableEntityType=variable.parent.entity_type,
        )


# GET, POST /plant-configurations/{configName}/kpis
class KPIItem(BaseModel):
    name: str
    variables: List[str] = Field(..., examples=["outlet_temperature"])
    expression: str = Field(..., examples=["{{ht-01outlet_temperature}} / 100"])
    uuid_str: Optional[str] = Field(None, examples=["234sdf2sdfagsdf"])

    @classmethod
    def hydrate_schema(cls, obj: core.at.KPI, atlas: core.at.AtlasRoot) -> KPIItem:
        var_uids = obj.variable_uids
        UUID_STR = str(obj.uid)

        variables = []
        for var_uid in var_uids:
            # get var
            var = atlas.variables_collection.get_item(var_uid)
            # get ui label via collection
            variables.append(obj._transform_uid_str_to_kpi_label(str(var_uid)))

        return KPIItem(
            name=obj.label,
            variables=variables,
            expression=obj.get_ui_expression(),
            uuid_str=UUID_STR,
        )

    def bootstrap_obj(
        self, variable_collection: core.at.VariableUIDCollection
    ) -> core.at.KPI:
        return core.at.KPI(
            label=self.name,
            expression=self.expression,
            collection_reference=variable_collection,
        )


##############

# SURROGATE TRAINING


class SurrogateTrainingConfig(BaseModel):
    model_type: Literal[
        "High Accuracy, Long Training", "Low Accuracy, Quick Training"
    ] = Field(
        default="High Accuracy, Long Training",
        description="Type of model to train (e.g., regression, classification)",
        examples=["High Accuracy, Long Training Time", "...", "..."],
    )
    training_regime: Literal["imported_data", "simulated_data"] = Field(
        default="imported_data",
        description="Training methodology or approach to use",
        examples=["", "via Training Data", "via Simulator Generated Data"],
    )
    training_data: str = Field(
        ...,
        description="Base64 encoded CSV data for model training",
        examples=["base64_encoded_string_here"],
    )
    csvCol2varUUID: Dict[str, uuid.UUID] = Field(
        ...,
        description="Mapping between CSV column names and variable UUIDs",
        examples=[{"temperature": "123e4567-e89b-12d3-a456-************"}],
    )
    timestep_col: Optional[str] = Field(
        None,
        description="Column name that contains timestep identifiers (optional)",
        examples=["timestamp", "sequence_id"],
    )
    timeset_col: Optional[str] = Field(
        None,
        description="Column name that contains timeset identifiers (optional)",
        examples=["timestamp", "sequence_id"],
    )

    @model_validator(mode="after")
    def validate_training_data_columns(self) -> "SurrogateTrainingConfig":
        """
        Automatically validates the training data columns when the model is instantiated
        """
        self.validate_all()
        return self

    def validate_all(self):
        """
        Validates that:
        1. All CSV column names in csvCol2varUUID exist in the training data
        2. The timestep column, if provided, exists in the training data

        Raises:
            ValueError: If validation fails with details about what failed
        """
        try:
            # Convert Base64 string to DataFrame
            df = base64_to_dataframe(self.training_data)

            # Get column names from the DataFrame
            df_columns = set(df.columns)

            # Validate csvCol2varUUID keys
            csv_cols = set(self.csvCol2varUUID.keys())
            missing_cols = csv_cols - df_columns
            if missing_cols:
                raise ValueError(
                    f"The following columns specified in csvCol2varUUID do not exist in the training data: {missing_cols}"
                )

            # Validate timestep column if provided
            if self.timestep_col and self.timestep_col not in df_columns:
                raise ValueError(
                    f"The specified timestep column '{self.timestep_col}' does not exist in the training data"
                )

            if self.timeset_col and self.timeset_col not in df_columns:
                raise ValueError(
                    f"The specified timestep column '{self.timestep_col}' does not exist in the training data"
                )

            return True

        except Exception as e:
            # Wrap any other exceptions with additional context
            if isinstance(e, ValueError):
                raise e
            else:
                raise ValueError(f"Validation failed: {str(e)}")


class SurrogateTrainingResults(BaseModel):
    section1_header: str = Field(
        ...,
        description="Title for the first results section",
        examples=["Training Performance"],
    )
    section1_body: str = Field(
        ...,
        description="Detailed text content for the first results section",
        examples=[
            "The model achieved an accuracy of 95% with minimal loss after 100 epochs."
        ],
    )
    section2_header: str = Field(
        ...,
        description="Title for the second results section",
        examples=["Validation Results"],
    )
    section2_body: str = Field(
        ...,
        description="Detailed text content for the second results section",
        examples=[
            "Model performance on validation data showed 92% accuracy with acceptable generalization."
        ],
    )
    plot1: Dict[str, List[float]] = Field(
        ...,
        description="Column-oriented data for the first visualization plot",
        examples=[{"x": [1, 2, 3, 4], "y": [10, 20, 15, 25], "label": [1, 1, 0, 0]}],
    )
    plot2: Dict[str, List[float]] = Field(
        ...,
        description="Column-oriented data for the second visualization plot",
        examples=[
            {
                "x": [1, 2, 3, 4],
                "y": [5, 10, 15, 20],
                "predicted": [4.8, 10.2, 14.8, 19.5],
            }
        ],
    )


class Status(BaseModel):
    """
    Represents the current status of any process.
    """

    status: Literal["Setup Required", "In Process", "Ready"] = Field(
        ...,
        description="Current status of the model training process",
        examples=["In Process"],
    )
