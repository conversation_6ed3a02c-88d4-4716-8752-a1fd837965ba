"""
Tests for TrainerRegistry Docker Image Management

This module tests the enhanced TrainerRegistry functionality for managing
Docker base images across different training services (Azure ML, local, AWS, GCP).

Test Coverage:
- Base image retrieval for different algorithms and training services
- Image registration and management
- Error handling for unsupported combinations
- Training service discovery
- Image mapping validation
"""

import pytest
from backend.core._surrogate._enums import EnumSurrogateAlgorithm
from backend.core._surrogate.trainers.trainer_registry import (
    TrainerRegistry, 
    TrainerRegistryError,
    get_default_trainer_registry,
    reset_default_trainer_registry
)


class TestTrainerRegistryDockerImages:
    """
    Tests for Docker image management functionality in TrainerRegistry.
    
    Following testing standards: no decorators, no mocks, Arrange-Act-Assert pattern.
    """
    
    def test_get_base_image_azure_rnn_success(self):
        """Test successful retrieval of Azure ML base image for RNN algorithm."""
        # Arrange: Create registry instance
        registry = TrainerRegistry()
        
        # Act: Get Azure base image for RNN
        image = registry.get_base_image(EnumSurrogateAlgorithm.RNN_TS, "azure")
        
        # Assert: Verify correct Azure ML image is returned
        assert image == "acpt-pytorch-2.2-cuda12.1", "Should return Azure Container for PyTorch image"
        
        print("Azure RNN base image retrieval test passed")
    
    def test_get_base_image_local_rnn_success(self):
        """Test successful retrieval of local development base image for RNN algorithm."""
        # Arrange: Create registry instance
        registry = TrainerRegistry()
        
        # Act: Get local base image for RNN
        image = registry.get_base_image(EnumSurrogateAlgorithm.RNN_TS, "local")
        
        # Assert: Verify correct local PyTorch image is returned
        assert image == "pytorch/pytorch:2.4.1-cuda12.1-cudnn9-devel", "Should return official PyTorch image"
        
        print("Local RNN base image retrieval test passed")
    
    def test_get_base_image_aws_rnn_success(self):
        """Test successful retrieval of AWS SageMaker base image for RNN algorithm."""
        # Arrange: Create registry instance
        registry = TrainerRegistry()
        
        # Act: Get AWS base image for RNN
        image = registry.get_base_image(EnumSurrogateAlgorithm.RNN_TS, "aws")
        
        # Assert: Verify correct AWS SageMaker image is returned
        expected_image = "763104351884.dkr.ecr.us-west-2.amazonaws.com/pytorch-training:2.4.1-gpu-py311-cu121-ubuntu20.04-sagemaker"
        assert image == expected_image, "Should return AWS SageMaker PyTorch image"
        
        print("AWS RNN base image retrieval test passed")
    
    def test_get_base_image_unsupported_algorithm(self):
        """Test error handling for unsupported algorithm."""
        # Arrange: Create registry instance
        registry = TrainerRegistry()
        
        # Act & Assert: Expect TrainerRegistryError for unsupported algorithm
        try:
            registry.get_base_image(EnumSurrogateAlgorithm.RANDOM_FOREST, "azure")
            assert False, "Should have raised TrainerRegistryError for unsupported algorithm"
        except TrainerRegistryError as e:
            assert "No base image mapping found" in str(e), "Error should mention missing mapping"
            assert "RANDOM_FOREST" in str(e), "Error should mention the specific algorithm"
        
        print("Unsupported algorithm error handling test passed")
    
    def test_get_base_image_unsupported_training_service(self):
        """Test error handling for unsupported training service."""
        # Arrange: Create registry instance
        registry = TrainerRegistry()
        
        # Act & Assert: Expect TrainerRegistryError for unsupported training service
        try:
            registry.get_base_image(EnumSurrogateAlgorithm.RNN_TS, "kubernetes")
            assert False, "Should have raised TrainerRegistryError for unsupported service"
        except TrainerRegistryError as e:
            assert "Training service 'kubernetes' not supported" in str(e), "Error should mention unsupported service"
            assert "Available services:" in str(e), "Error should list available services"
        
        print("Unsupported training service error handling test passed")
    
    def test_register_base_image_success(self):
        """Test successful registration of new base image mapping."""
        # Arrange: Create registry instance
        registry = TrainerRegistry()
        custom_image = "my-custom-pytorch:2.4.1-cuda12.1"
        
        # Act: Register custom image for local training
        registry.register_base_image(EnumSurrogateAlgorithm.RNN_TS, "local", custom_image)
        
        # Assert: Verify custom image is now returned
        retrieved_image = registry.get_base_image(EnumSurrogateAlgorithm.RNN_TS, "local")
        assert retrieved_image == custom_image, "Should return the newly registered custom image"
        
        print("Base image registration test passed")
    
    def test_register_base_image_new_algorithm(self):
        """Test registration of base image for new algorithm."""
        # Arrange: Create registry instance
        registry = TrainerRegistry()
        custom_image = "scikit-learn:1.5.0"
        
        # Act: Register image for algorithm that doesn't have existing mappings
        registry.register_base_image(EnumSurrogateAlgorithm.RANDOM_FOREST, "local", custom_image)
        
        # Assert: Verify new algorithm mapping was created
        retrieved_image = registry.get_base_image(EnumSurrogateAlgorithm.RANDOM_FOREST, "local")
        assert retrieved_image == custom_image, "Should return image for newly registered algorithm"
        
        print("New algorithm image registration test passed")
    
    def test_get_supported_training_services_success(self):
        """Test retrieval of supported training services for an algorithm."""
        # Arrange: Create registry instance
        registry = TrainerRegistry()
        
        # Act: Get supported services for RNN algorithm
        services = registry.get_supported_training_services(EnumSurrogateAlgorithm.RNN_TS)
        
        # Assert: Verify all expected services are present
        expected_services = ["azure", "local", "aws", "gcp"]
        assert set(services) == set(expected_services), f"Should return all configured services: {expected_services}"
        
        print("Supported training services retrieval test passed")
    
    def test_get_supported_training_services_unsupported_algorithm(self):
        """Test error handling when getting services for unsupported algorithm."""
        # Arrange: Create registry instance
        registry = TrainerRegistry()
        
        # Act & Assert: Expect TrainerRegistryError for unsupported algorithm
        try:
            registry.get_supported_training_services(EnumSurrogateAlgorithm.GRADIENT_BOOSTING)
            assert False, "Should have raised TrainerRegistryError for unsupported algorithm"
        except TrainerRegistryError as e:
            assert "Algorithm" in str(e) and "is not supported" in str(e), "Error should mention unsupported algorithm"
        
        print("Unsupported algorithm services query error handling test passed")
    
    def test_get_algorithm_image_summary(self):
        """Test retrieval of complete algorithm-to-image mapping summary."""
        # Arrange: Create registry instance
        registry = TrainerRegistry()
        
        # Act: Get complete image summary
        summary = registry.get_algorithm_image_summary()
        
        # Assert: Verify summary structure and content
        assert isinstance(summary, dict), "Summary should be a dictionary"
        assert EnumSurrogateAlgorithm.RNN_TS in summary, "Summary should include RNN_TS algorithm"
        
        rnn_images = summary[EnumSurrogateAlgorithm.RNN_TS]
        assert "azure" in rnn_images, "RNN images should include Azure mapping"
        assert "local" in rnn_images, "RNN images should include local mapping"
        assert rnn_images["azure"] == "acpt-pytorch-2.2-cuda12.1", "Azure image should match expected value"
        
        print("Algorithm image summary retrieval test passed")


# ==================================================
# EXAMPLE USAGE DEMONSTRATION
# ==================================================

def demonstrate_docker_image_management():
    """
    Demonstrate the Docker image management functionality.
    
    This function shows practical usage examples of the enhanced TrainerRegistry
    for managing Docker images across different training services.
    """
    print("\n" + "="*60)
    print("Docker Image Management Demonstration")
    print("="*60)
    
    # Create registry instance
    registry = TrainerRegistry()
    
    # Example 1: Get images for different training services
    print("\n1. Getting base images for RNN training across services:")
    services = ["azure", "local", "aws", "gcp"]
    
    for service in services:
        try:
            image = registry.get_base_image(EnumSurrogateAlgorithm.RNN_TS, service)
            print(f"   {service:8}: {image}")
        except TrainerRegistryError as e:
            print(f"   {service:8}: ERROR - {e}")
    
    # Example 2: Register custom image
    print("\n2. Registering custom image for local development:")
    custom_image = "my-org/pytorch-custom:2.4.1-cuda12.1-optimized"
    registry.register_base_image(EnumSurrogateAlgorithm.RNN_TS, "local", custom_image)
    
    updated_image = registry.get_base_image(EnumSurrogateAlgorithm.RNN_TS, "local")
    print(f"   Updated local image: {updated_image}")
    
    # Example 3: Check supported services
    print("\n3. Supported training services for RNN:")
    supported = registry.get_supported_training_services(EnumSurrogateAlgorithm.RNN_TS)
    print(f"   Services: {', '.join(supported)}")
    
    # Example 4: Get complete summary
    print("\n4. Complete algorithm-image mapping summary:")
    summary = registry.get_algorithm_image_summary()
    for algorithm, service_images in summary.items():
        print(f"   {algorithm.value}:")
        for service, image in service_images.items():
            print(f"     {service}: {image}")
    
    print("\n" + "="*60)
    print("Demonstration completed successfully!")
    print("="*60)


# ==================================================
# TEST RUNNER
# ==================================================

if __name__ == "__main__":
    print("Running TrainerRegistry Docker Image Management tests...")
    print("="*60)
    
    # Create test instance
    image_tests = TestTrainerRegistryDockerImages()
    
    # Run all test methods
    try:
        image_tests.test_get_base_image_azure_rnn_success()
        image_tests.test_get_base_image_local_rnn_success()
        image_tests.test_get_base_image_aws_rnn_success()
        image_tests.test_get_base_image_unsupported_algorithm()
        image_tests.test_get_base_image_unsupported_training_service()
        image_tests.test_register_base_image_success()
        image_tests.test_register_base_image_new_algorithm()
        image_tests.test_get_supported_training_services_success()
        image_tests.test_get_supported_training_services_unsupported_algorithm()
        image_tests.test_get_algorithm_image_summary()
        
        print("\n✅ All Docker image management tests passed successfully!")
        
        # Run demonstration
        demonstrate_docker_image_management()
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        raise
