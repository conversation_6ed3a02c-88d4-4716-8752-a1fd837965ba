"""
Unit Tests for AzureMLRunner

This module tests the AzureMLRunner implementation to ensure it properly
implements the TrainingRunnerPort interface while providing cloud-based
training execution on Azure ML.

Test Coverage:
- AzureMLRunner initialization and configuration
- Infrastructure setup and teardown
- Training job submission and monitoring
- Error handling and recovery mechanisms
- Template method pattern implementation
- Azure ML integration points
"""

import pytest
import sys
import uuid
import os
import logging
import pathlib
import tempfile
import dotenv
import json

# Serialize model and job
import pickle
import base64
from typing import Any, Dict
import torch
import torch.nn as nn
import backend.core._surrogate as su
from backend.infrastructure.runners.surrogate_trainers.runner_azureml import (
    VOAzureMLComputeConfig,
    AzureMLClientFactory,
    AzureMLTrainingRunner
)
from backend.core.interfaces.surrogate_interfaces import SurrogateConfigError
from backend.core._surrogate.factories import RNNSurrogateTrainerFactory

# ============================================================================
# SHARED TEST UTILITIES
# ============================================================================

def _create_test_runner():
    """Create minimal runner instance to access methods - shared across all test classes."""

    class TestRunner(AzureMLTrainingRunner):
        def setup_infrastructure(self): pass
        def teardown_infrastructure(self): pass

    runner = TestRunner.__new__(TestRunner)  # Create without calling __init__
    runner.logger = logging.getLogger(__name__)  # Add logger for method calls
    return runner


def _check_azure_credentials():
    """Check if Azure credentials are available for integration testing - shared utility."""
    dotenv.load_dotenv()
    required_vars = [
        "AZURE_CLIENT_ID",
        "AZURE_TENANT_ID",
        "AZURE_CLIENT_SECRET",
        "AZ_SUBSCRIPTION_ID",
        "AZ_RESOURCE_GROUP",
        "AZ_ML_WORKSPACE"
    ]
    
    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    if missing_vars:
        pytest.skip(f"Azure integration test skipped - missing env vars: {missing_vars}")
    return True


def _create_simple_training_script() -> pathlib.Path:
    """Create a minimal training script for testing - simplified version."""
    script_content = '''#!/usr/bin/env python3
import argparse
import json
import sys
from pathlib import Path

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--job_configs_path", type=str, required=True)
    parser.add_argument("--training_data_path", type=str, required=True)
    parser.add_argument("--test_data_path", type=str, required=False)
    parser.add_argument("--output_dir", type=str, required=True)

    args = parser.parse_args()

    print(f"Azure ML Job Started!")
    print(f"Job ID: {args.job_configs_path}")
    print(f"Training data: {args.training_data_path}")
    print(f"Output dir: {args.output_dir}")

    # Create output
    output_path = Path(args.output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    result = {"status": "completed", "message": "Test job completed"}
    with open(output_path / "result.json", "w") as f:
        json.dump(result, f)

    print("Job completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
'''
    
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False)
    temp_file.write(script_content)
    temp_file.close()
    
    script_path = pathlib.Path(temp_file.name)
    script_path.chmod(0o755)
    return script_path

# Define model at module level so it can be properly pickled
class SimpleModel(nn.Module):
    def __init__(self, input_size: int = 10, hidden_size: int = 8, output_size: int = 2) -> None:
        super().__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.fc2 = nn.Linear(hidden_size, output_size)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.fc1(x)
        x = self.relu(x)
        x = self.fc2(x)
        return x

def create_simple_torch_model() -> torch.nn.Module:
    """
    Create a super simple torch model that simulates a trained model.
    
    Returns:
        torch.nn.Module: A simple PyTorch neural network with trained weights
    """
    try:
        # Create model with fixed seed for reproducibility
        torch.manual_seed(42)
        model = SimpleModel()
        
        # Simulate training by setting specific weights
        # This makes it behave like a trained model without actual training
        with torch.no_grad():
            # Set fc1 weights and bias
            model.fc1.weight.fill_(0.1)
            model.fc1.bias.fill_(0.01)
            
            # Set fc2 weights and bias
            model.fc2.weight.fill_(0.2)
            model.fc2.bias.fill_(0.02)
        
        # Verify model produces non-random output
        test_input = torch.ones(1, 10)
        test_output = model(test_input)
        
        # Set model to eval mode as would be done after training
        model.eval()
        
        return model
    
    except ImportError:
        pytest.skip("PyTorch not installed, skipping torch model creation")

def create_simple_training_script() -> pathlib.Path:
    """
    Create a simple training script focused on testing polling and artifact download.

    This script is minimal and focused on the core testing goal:
    - Creates expected output files without complex dependencies
    - Tests that polling works correctly
    - Tests that artifact download works
    - No module imports that could cause issues

    The script takes --output_dir as argument and saves:
    - raw_model.pth: Simple PyTorch state dict
    - training_job.json: Simple JSON metadata
    - training.log: Execution log for debugging

    Returns:
        pathlib.Path: Path to the created script file
    """
    script_content = '''#!/usr/bin/env python3
import argparse
import json
import sys
import time
from pathlib import Path
import torch
import torch.nn as nn
from typing import Dict, Any

# Simple model class for testing
class SimpleModel(nn.Module):
    def __init__(self, input_size: int = 10, hidden_size: int = 8, output_size: int = 2) -> None:
        super().__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.fc2 = nn.Linear(hidden_size, output_size)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.fc1(x)
        x = self.relu(x)
        x = self.fc2(x)
        return x

def log_message(message: str, log_file: Path) -> None:
    """Write message to both stdout and log file."""
    print(message)
    with open(log_file, "a") as f:
        f.write(f"{message}\\n")

def main() -> int:
    parser = argparse.ArgumentParser(description="Unified training script for model serialization testing")
    parser.add_argument("--output_dir", type=str, required=True, help="Output directory for artifacts")
    args = parser.parse_args()

    # Create output directory
    output_path = Path(args.output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # Initialize logging
    log_file = output_path / "training.log"
    log_message("=== Training Script Started ===", log_file)
    log_message(f"Output directory: {output_path}", log_file)

    try:
        # Create simple test model and data (no complex dependencies)
        log_message("Creating simple test model and data...", log_file)

        # Create a simple model for testing
        model = SimpleModel()
        model.eval()

        # Create simple job metadata for testing
        job_data = {
            "uid": "test-job-12345",
            "status": "completed",
            "model_type": "SimpleModel",
            "created_at": "2025-06-29"
        }

        log_message(f"Model type: {type(model).__name__}", log_file)
        log_message(f"Job UID: {job_data.get('uid', 'unknown')}", log_file)

        # Save model using torch.save (PyTorch best practice)
        model_pth_path = output_path / "raw_model.pth"
        torch.save(model.state_dict(), model_pth_path)
        log_message(f"Saved PyTorch state dict to: {model_pth_path}", log_file)

        # Also save full model using torch.save for complete compatibility
        full_model_path = output_path / "raw_model_full.pth"
        torch.save(model, full_model_path)
        log_message(f"Saved full PyTorch model to: {full_model_path}", log_file)

        # Save model info as text for backward compatibility
        model_pkl_path = output_path / "raw_model.pkl"
        with open(model_pkl_path, "w") as f:
            f.write(f"Model info: {type(model).__name__}\\nParameters: {sum(p.numel() for p in model.parameters())}\\n")
        log_message(f"Saved model info to: {model_pkl_path}", log_file)

        # Save job data using JSON
        job_path = output_path / "training_job.json"
        with open(job_path, "w") as f:
            json.dump(job_data, f, indent=2)
        log_message(f"Saved job data to: {job_path}", log_file)

        # Create metadata file
        metadata = {
            "model_type": type(model).__name__,
            "job_uid": job_data.get('uid', 'test-job-12345'),
            "files_created": [
                "raw_model.pth",
                "raw_model_full.pth",
                "raw_model.pkl",
                "training_job.json",
                "training.log",
                "metadata.json"
            ],
            "status": "completed"
        }

        metadata_path = output_path / "metadata.json"
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)
        log_message(f"Saved metadata to: {metadata_path}", log_file)
        log_message(f"Saved training log to: {log_file}", log_file)

        log_message("=== Training Script Completed Successfully ===", log_file)
        return 0

    except Exception as e:
        error_msg = f"Training script failed: {str(e)}"
        log_message(f"ERROR: {error_msg}", log_file)
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''

    # No complex embedding needed - script is self-contained

    # Write to file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False)
    temp_file.write(script_content)
    temp_file.close()

    script_path = pathlib.Path(temp_file.name)
    script_path.chmod(0o755)
    return script_path


def create_sequence_test_dataset() -> su.VODataset:
    """Create a test dataset suitable for sequence/RNN training."""
    import numpy as np
    import uuid

    # Create 3D arrays for sequence data (timesets, timesteps, features)
    n_timesets = 150  # Sufficient for training split
    n_timesteps = 10
    n_features = 3
    n_outputs = 1

    # Generate synthetic time series data
    np.random.seed(42)  # For reproducibility
    arr_x = np.random.randn(n_timesets, n_timesteps, n_features)
    arr_y = np.random.randn(n_timesets, n_timesteps, n_outputs)
    return su.VODataset(
        arr_x=arr_x,
        arr_y=arr_y,
        transformer_uid=uuid.uuid4(),
        colnames_x=[f'feature_{i}' for i in range(n_features)],
        colnames_y=[f'target_{i}' for i in range(n_outputs)],
        pattern="sequence"
    )

def create_test_job() -> su.ENTTrainingJob:
    config = RNNSurrogateTrainerFactory.create_complete_config("fast")
    metadata = config["metadata"]
    training_config = config["training_config"]
    model_config = config["model_config"]
    hpo_config = config["hpo_config"]

    return su.ENTTrainingJob(
        metadata=metadata,
        training_configuration=training_config,
        model_configuration=model_config,
        hpo_configuration=hpo_config
    )
    

# ==================================================
# TESTS
# ==================================================

class TestAzureMLClientFactory:
    """
    Comprehensive tests for AzureMLClientFactory.

    Tests the factory pattern implementation for Azure ML client creation,
    credential management, and environment variable handling.
    Using direct environment variable manipulation, and Arrange-Act-Assert pattern.
    """

    def _backup_env_vars(self):
        """Helper method to backup current environment variables."""
        return {
            'AZ_SUBSCRIPTION_ID': os.environ.get('AZ_SUBSCRIPTION_ID'),
            'AZ_RESOURCE_GROUP': os.environ.get('AZ_RESOURCE_GROUP'),
            'AZ_ML_WORKSPACE': os.environ.get('AZ_ML_WORKSPACE')
        }

    def _restore_env_vars(self, backup):
        """Helper method to restore environment variables."""
        for key, value in backup.items():
            if value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = value

    def _clear_env_vars(self):
        """Helper method to clear Azure environment variables."""
        os.environ.pop('AZ_SUBSCRIPTION_ID', None)
        os.environ.pop('AZ_RESOURCE_GROUP', None)
        os.environ.pop('AZ_ML_WORKSPACE', None)

    def test_initialization_with_explicit_params(self):
        """Test factory initialization with all parameters explicitly provided."""
        # Arrange: Set up explicit parameters
        subscription_id = "test-subscription-123"
        resource_group = "test-rg"
        workspace_name = "test-workspace"

        # Act: Create factory with explicit parameters
        factory = AzureMLClientFactory(
            subscription_id=subscription_id,
            resource_group=resource_group,
            workspace_name=workspace_name
        )

        # Assert: Verify parameters are set correctly
        assert factory.subscription_id == subscription_id, "Subscription ID should match input"
        assert factory.resource_group == resource_group, "Resource group should match input"
        assert factory.workspace_name == workspace_name, "Workspace name should match input"

        print("Initialization with explicit parameters test passed")

    def test_initialization_with_env_vars(self):
        """Test factory initialization using environment variables."""
        # Arrange: Backup current environment and set test values
        env_backup = self._backup_env_vars()

        try:
            os.environ['AZ_SUBSCRIPTION_ID'] = 'env-subscription-456'
            os.environ['AZ_RESOURCE_GROUP'] = 'env-rg'
            os.environ['AZ_ML_WORKSPACE'] = 'env-workspace'

            # Act: Create factory without explicit parameters
            factory = AzureMLClientFactory()

            # Assert: Verify environment variables are used
            assert factory.subscription_id == 'env-subscription-456', "Should use env var for subscription"
            assert factory.resource_group == 'env-rg', "Should use env var for resource group"
            assert factory.workspace_name == 'env-workspace', "Should use env var for workspace"

            print("Initialization with environment variables test passed")

        finally:
            # Cleanup: Restore original environment
            self._restore_env_vars(env_backup)

    def test_validation_success(self):
        """Test that validation passes when all required environment variables are present."""
        # Arrange: Backup current environment and set valid values
        env_backup = self._backup_env_vars()

        try:
            os.environ['AZ_SUBSCRIPTION_ID'] = 'valid-subscription'
            os.environ['AZ_RESOURCE_GROUP'] = 'valid-rg'
            os.environ['AZ_ML_WORKSPACE'] = 'valid-workspace'

            # Act: Create factory (validation happens in __init__)
            factory = AzureMLClientFactory()

            # Assert: No exception should be raised, factory should be created successfully
            assert factory.subscription_id is not None, "Subscription ID should be set"
            assert factory.resource_group is not None, "Resource group should be set"
            assert factory.workspace_name is not None, "Workspace name should be set"

            print("Validation success test passed")

        finally:
            # Cleanup: Restore original environment
            self._restore_env_vars(env_backup)

    def test_validation_failure_missing_subscription(self):
        """Test validation failure when subscription_id is missing."""
        # Arrange: Backup current environment and clear all Azure variables
        env_backup = self._backup_env_vars()

        try:
            self._clear_env_vars()

            # Act & Assert: Expect SurrogateConfigError for missing subscription
            try:
                AzureMLClientFactory()
                assert False, "Should have raised SurrogateConfigError for missing subscription"
            except SurrogateConfigError as e:
                assert "AZ_SUBSCRIPTION_ID" in str(e), "Error should mention missing subscription ID"

            print("Validation failure for missing subscription test passed")

        finally:
            # Cleanup: Restore original environment
            self._restore_env_vars(env_backup)

    def test_validation_failure_missing_resource_group(self):
        """Test validation failure when resource_group is missing."""
        # Arrange: Backup current environment and set only subscription ID
        env_backup = self._backup_env_vars()

        try:
            self._clear_env_vars()
            os.environ['AZ_SUBSCRIPTION_ID'] = 'test-sub'

            # Act & Assert: Expect SurrogateConfigError for missing resource group
            try:
                AzureMLClientFactory()
                assert False, "Should have raised SurrogateConfigError for missing resource group"
            except SurrogateConfigError as e:
                assert "AZ_RESOURCE_GROUP" in str(e), "Error should mention missing resource group"

            print("Validation failure for missing resource group test passed")

        finally:
            # Cleanup: Restore original environment
            self._restore_env_vars(env_backup)

    def test_validation_failure_missing_workspace(self):
        """Test validation failure when workspace_name is missing."""
        # Arrange: Backup current environment and set subscription + resource group only
        env_backup = self._backup_env_vars()

        try:
            self._clear_env_vars()
            os.environ['AZ_SUBSCRIPTION_ID'] = 'test-sub'
            os.environ['AZ_RESOURCE_GROUP'] = 'test-rg'

            # Act & Assert: Expect SurrogateConfigError for missing workspace
            try:
                AzureMLClientFactory()
                assert False, "Should have raised SurrogateConfigError for missing workspace"
            except SurrogateConfigError as e:
                assert "AZ_ML_WORKSPACE" in str(e), "Error should mention missing workspace"

            print("Validation failure for missing workspace test passed")

        finally:
            # Cleanup: Restore original environment
            self._restore_env_vars(env_backup)

    def test_create_ml_client_success(self):
        """Test successful MLClient creation with proper credential handling."""
        # Arrange: Create testable factory with valid parameters
        factory = AzureMLClientFactory(
            subscription_id="test-sub",
            resource_group="test-rg",
            workspace_name="test-workspace"
        )

        # Act: Create ML client
        client = factory.create_ml_client()

        # Assert: Verify client creation
        assert client is not None, "Should return a client instance"
        # Note: Real MLClient doesn't expose internal parameters for testing
        # We focus on successful creation rather than internal state validation

        print("MLClient creation success test passed")

    def test_create_ml_client_with_auth_failure(self):
        """Test handling of authentication failures during client creation."""
        # Note: This test demonstrates auth failure handling but may not always fail
        # since DefaultAzureCredential tries multiple authentication methods

        # Arrange: Create factory with potentially invalid credentials
        factory = AzureMLClientFactory(
            subscription_id="invalid-subscription-id",
            resource_group="invalid-resource-group",
            workspace_name="invalid-workspace"
        )

        # Act: Attempt client creation
        try:
            factory.create_ml_client()
            # If this succeeds, DefaultAzureCredential found valid auth
            print("MLClient creation succeeded (valid auth found)")
        except Exception as e:
            # Expected case for invalid credentials
            print(f"Authentication failure handled correctly: {type(e).__name__}")

        print("Authentication failure handling test completed")

    def test_real_azure_ml_client_integration(self):
        """
        Integration test with real Azure ML client to validate credentials and configuration.

        This test uses the actual AzureMLClientFactory (not the testable subclass) to create
        a real MLClient and verify that Azure credentials, subscription, resource group,
        and workspace are valid and accessible.

        Note: This test requires valid Azure credentials and configuration to be present
        in environment variables or through DefaultAzureCredential authentication chain.
        If credentials are not available, the test will be skipped with a clear message.
        """
        # Arrange: Check if Azure environment variables are available
        required_env_vars = ['AZ_SUBSCRIPTION_ID', 'AZ_RESOURCE_GROUP', 'AZ_ML_WORKSPACE', 'AZURE_CLIENT_ID', 'AZURE_TENANT_ID', 'AZURE_CLIENT_SECRET']
        missing_vars = [var for var in required_env_vars if not os.environ.get(var)]

        if missing_vars:
            logging.info(f"⏭️ Skipping real Azure integration test - missing environment variables: {missing_vars}")
            logging.info(f"To run this test, set required_env_vars")
            return

        factory = AzureMLClientFactory()

        try:
            # Create MLClient and test connectivity
            ml_client = factory.create_ml_client()

            # Assert: Verify client was created successfully
            assert ml_client is not None, "MLClient should be created successfully"

            # Verify client has expected attributes
            assert hasattr(ml_client, 'workspaces'), "MLClient should have workspaces attribute"
            assert hasattr(ml_client, 'compute'), "MLClient should have compute attribute"
            assert hasattr(ml_client, 'environments'), "MLClient should have environments attribute"

            # Test basic connectivity by attempting to get workspace info
            # This validates that credentials, subscription, resource group, and workspace are all valid
            workspace_info = ml_client.workspaces.get(factory.workspace_name)
            assert workspace_info is not None, "Should be able to retrieve workspace information"
            assert workspace_info.name == factory.workspace_name, "Workspace name should match configuration"

            logging.info("✅ Real Azure ML client integration test passed")
            logging.info(f"   Successfully connected to workspace: {workspace_info.name}")
            logging.info(f"   Resource group: {factory.resource_group}")
            logging.info(f"   Subscription: {factory.subscription_id}")

        except Exception as e:
            # Provide helpful error messages for common authentication issues
            error_msg = str(e).lower()

            if "authentication" in error_msg or "credential" in error_msg:
                logging.error(f"❌ Authentication failed: {str(e)}")
                logging.error("   Ensure you have valid Azure credentials via one of:")
                logging.error("   - Azure CLI: Run 'az login'")
                logging.error("   - Environment variables: AZURE_CLIENT_ID, AZURE_TENANT_ID, AZURE_CLIENT_SECRET")
                logging.error("   - Managed Identity (when running on Azure resources)")
            elif "subscription" in error_msg:
                logging.error(f"❌ Subscription access failed: {str(e)}")
                logging.error(f"   Verify subscription ID '{factory.subscription_id}' is correct and accessible")
            elif "resource" in error_msg or "group" in error_msg:
                logging.error(f"❌ Resource group access failed: {str(e)}")
                logging.error(f"   Verify resource group '{factory.resource_group}' exists and is accessible")
            elif "workspace" in error_msg:
                logging.error(f"❌ Workspace access failed: {str(e)}")
                logging.error(f"   Verify ML workspace '{factory.workspace_name}' exists in resource group '{factory.resource_group}'")
            else:
                logging.error(f"❌ Unexpected error during Azure integration test: {str(e)}")

            # Re-raise the exception to fail the test
            raise

class TestProvisionComputeIntegration:
    """
    Integration tests for compute target provisioning with real Azure ML services.
    Tests compute target creation and validation for different configuration types:
    """

    def _create_test_config(self, compute_type, test_compute_name: str) -> VOAzureMLComputeConfig:
        """Create test configuration with custom compute target name."""
        base_config = VOAzureMLComputeConfig.create(compute_type)
        return VOAzureMLComputeConfig(
            compute_label=test_compute_name,
            instance_type=base_config.instance_type,
            instance_count=base_config.instance_count,
            environment_name=base_config.environment_name,
            environment_version=base_config.environment_version,
            experiment_name=base_config.experiment_name,
            job_timeout_minutes=base_config.job_timeout_minutes,
            idle_min_before_scale_down=base_config.idle_min_before_scale_down,
            max_concurrent_jobs=base_config.max_concurrent_jobs,
            auto_scale_enabled=base_config.auto_scale_enabled
        )


    def _cleanup_compute_target(self, ml_client, compute_name: str, *, trigger: bool = True):
        """Clean up compute target created during testing."""
        if not trigger:
            return

        try:
            # Check if compute exists before attempting deletion
            existing_compute = ml_client.compute.get(compute_name)
            if existing_compute:
                logging.info(f"Cleaning up test compute target: {compute_name}")
                ml_client.compute.begin_delete(compute_name)
        except Exception as e:
            # Compute doesn't exist or deletion failed - log but don't fail test
            logging.warning(f"Cleanup warning for {compute_name}: {e}")

    def test_provision_gpu_performant_compute(self, to_cleanup: bool = True) -> str:
        """Test compute provisioning with gpu_performant configuration."""

        _check_azure_credentials()
        compute_label = None

        # Arrange: Create gpu_performant configuration with test compute name
        test_compute_name = f"test-gpu-perf-{uuid.uuid4().hex[:8]}"
        config = self._create_test_config("gpu_performant", test_compute_name)

        # Create ML client and runner instance
        client_factory = AzureMLClientFactory()
        ml_client = client_factory.create_ml_client()

        # Create minimal runner instance just to access the method
        runner =_create_test_runner()

        try:
            # Act: Call _provision_compute method directly
            compute_label = runner._provision_compute(
                job_id="test-job-gpu-perf",
                compute_configuration=config,
                ml_client=ml_client
            )

            # Assert: Verify compute target was created successfully
            assert  compute_label == test_compute_name

            # Verify compute target exists
            created_compute = ml_client.compute.get(test_compute_name)
            assert created_compute.name == test_compute_name

            logging.info(f"✅ GPU performant compute test passed: {test_compute_name}")

        finally:
            # Cleanup: Remove test compute target
            self._cleanup_compute_target(ml_client, test_compute_name, trigger = to_cleanup)
        
        if not to_cleanup:
            return compute_label
        else:
            return "gpu-cluster"
    

    def test_provision_gpu_budget_compute(self, trigger = True):
        """Test compute provisioning with gpu_budget configuration."""
        _check_azure_credentials()

        # Arrange: Create gpu_budget configuration with test compute name
        test_compute_name = f"test-gpu-budget-{uuid.uuid4().hex[:8]}"
        config = self._create_test_config("gpu_budget", test_compute_name)

        # Create ML client and runner instance
        client_factory = AzureMLClientFactory()
        ml_client = client_factory.create_ml_client()

        runner =_create_test_runner()

        try:
            # Act: Call _provision_compute method directly
            result = runner._provision_compute(
                job_id="test-job-gpu-budget",
                compute_configuration=config,
                ml_client=ml_client
            )

            # Assert: Verify budget configuration
            assert result == test_compute_name

            created_compute = ml_client.compute.get(test_compute_name)
            assert created_compute.name == test_compute_name

            logging.info(f"✅ GPU budget compute test passed: {test_compute_name}")

        finally:
            self._cleanup_compute_target(ml_client, test_compute_name, trigger = trigger)

    def z_test_provision_cpu_performant_compute(self, trigger = True):
        """Test compute provisioning with cpu_performant configuration."""
        _check_azure_credentials()

        # Arrange: Create cpu_performant configuration
        test_compute_name = f"test-cpu-perf-{uuid.uuid4().hex[:8]}"
        config = self._create_test_config("cpu_performant", test_compute_name)

        # Create ML client and runner instance
        client_factory = AzureMLClientFactory()
        ml_client = client_factory.create_ml_client()

        runner =_create_test_runner()

        try:
            # Act: Call _provision_compute method directly
            result = runner._provision_compute(
                job_id="test-job-cpu-perf",
                compute_configuration=config,
                ml_client=ml_client
            )

            # Assert: Verify CPU-optimized configuration
            assert result == test_compute_name

            created_compute = ml_client.compute.get(test_compute_name)
            assert created_compute.name == test_compute_name

            logging.info(f"✅ CPU performant compute test passed: {test_compute_name}")

        finally:
            self._cleanup_compute_target(ml_client, test_compute_name, trigger = trigger)

    def z_test_provision_cpu_budget_compute(self, tigger = True, trigger = False):
        """Test compute provisioning with cpu_budget configuration."""
        _check_azure_credentials()

        # Arrange: Create cpu_budget configuration
        test_compute_name = f"test-cpu-budget-{uuid.uuid4().hex[:8]}"
        config = self._create_test_config("cpu_budget", test_compute_name)

        # Create ML client and runner instance
        client_factory = AzureMLClientFactory()
        ml_client = client_factory.create_ml_client()

        runner =_create_test_runner()

        try:
            # Act: Call _provision_compute method directly
            result = runner._provision_compute(
                job_id="test-job-cpu-budget",
                compute_configuration=config,
                ml_client=ml_client
            )

            # Assert: Verify budget CPU configuration
            assert result == test_compute_name

            created_compute = ml_client.compute.get(test_compute_name)
            assert created_compute.name == test_compute_name

            logging.info(f"✅ CPU budget compute test passed: {test_compute_name}")

        finally:
            self._cleanup_compute_target(ml_client, test_compute_name, trigger = trigger)

    def test_provision_existing_compute_target(self):
        """Test behavior when compute target already exists."""
        _check_azure_credentials()

        # Arrange: Use default configuration and assume a common compute target exists
        config = self._create_test_config("cpu_performant", "cpu-cluster")

        # Create ML client and runner instance
        client_factory = AzureMLClientFactory()
        ml_client = client_factory.create_ml_client()

        runner =_create_test_runner()

        # Act: Call _provision_compute method directly
        result = runner._provision_compute(
            job_id="test-job-existing",
            compute_configuration=config,
            ml_client=ml_client
        )

        # Assert: Should return existing compute target name
        assert result == "cpu-cluster"
        logging.info("✅ Existing compute target test passed")


class TestProvisionEnvironmentIntegration:
    """
    Integration tests for environment setup with real Azure ML services.

    Tests environment creation and validation for different base images from TrainerRegistry.
    Covers PyTorch GPU environments, CPU environments, and versioning behavior.
    """


    def _cleanup_environment(self, env_name: str, env_version: str):
        """Clean up environment created during testing."""
        try:
            # Note: Azure ML environments cannot be deleted once created
            # They are immutable for reproducibility
            # This is a placeholder for any cleanup logic if needed
            logging.info(f"Environment {env_name}:{env_version} will remain (Azure ML environments are immutable)")
        except Exception as e:
            logging.warning(f"Cleanup warning for {env_name}:{env_version}: {e}")

    def test_setup_curated_pytorch_environment(self):
        """Test environment setup with PyTorch GPU base image."""
        print("Running test_setup_pytorch_gpu_environment")
        _check_azure_credentials()

        # Arrange: Create ML client and runner instance
        client_factory = AzureMLClientFactory()
        ml_client = client_factory.create_ml_client()

        runner =_create_test_runner()
        yml_path = pathlib.Path(__file__).parent / "environment.yml" # this is a mock yml
        environment_name = "azureml://registries/azureml/environments/acpt-pytorch-2.2-cuda12.1/version/37"

        try:
            # Act: Call _provision_environment method directly
            result = runner._provision_environment(
                job_id="test-env-pytorch-gpu",
                job=create_test_job(),
                ml_client=ml_client,
                environment_name=environment_name,
                conda_yml=yml_path
            )

            # Assert: Verify environment was created/found
            env_name, env_version = result.split(":")
            assert "conda" in env_name, "Should use conda environment for curated env + conda file"

            # Verify environment exists in Azure ML
            created_env = ml_client.environments.get(env_name, version=env_version)
            assert created_env is not None, "Should be able to retrieve created environment"
            assert created_env.name == env_name

            logging.info(f"✅ PyTorch GPU environment test passed: {result}")

        except Exception as e:
            logging.info(f"Environment test completed with: {e}")
            # Environment tests may fail due to permissions or resource constraints

    def z_test_setup_cpu_environment(self):
        """Test environment setup with CPU base image for traditional ML.
        TODO this needs a custom job to test. current job generates labels for RNN
        """
        _check_azure_credentials()

        # Arrange: Create ML client and runner instance
        client_factory = AzureMLClientFactory()
        ml_client = client_factory.create_ml_client()

        runner =_create_test_runner()

        # Get base image - use a simple CPU test image
        docker_base_image = "mcr.microsoft.com/azureml/sklearn-1.0-ubuntu20.04-py38-cpu-inference:latest"

        try:
            # Act: Call _provision_environment method directly
            result = runner._provision_environment(
                job_id="test-env-cpu",
                job=create_test_job(),
                ml_client=ml_client,
                environment_name=docker_base_image
            )

            # Assert: Verify environment was created/found
            assert "surrogate-rnn_ts-env:" in result
            env_name, env_version = result.split(":")

            # Verify environment exists in Azure ML
            created_env = ml_client.environments.get(env_name, version=env_version)
            assert created_env.name == env_name

            logging.info(f"✅ CPU environment test passed: {result}")

        except Exception as e:
            logging.info(f"Environment test completed with: {e}")
            # Environment tests may fail due to permissions or resource constraints


# ============================================================================
# TEST CLASSES  
# ============================================================================

class TestBuildTrainingCommand:
    """Tests for _build_training_command method - streamlined and focused."""

    def test_build_command_comprehensive(self):
        """Single comprehensive test covering all command building scenarios."""
        runner = _create_test_runner()
        script_path = _create_simple_training_script()

        try:
            # Test 1: Full arguments
            args_full = {
                "job_configs_path": pathlib.Path("/tmp/job_config.json"),
                "training_data_path": pathlib.Path("/tmp/training_data.pkl"),
                "test_data_path": pathlib.Path("/tmp/test_data.pkl"),
                "output_dir": pathlib.Path("/tmp/output")
            }
            
            command_full = runner._build_training_command(script_path, args_full)
            assert command_full.startswith("python")
            assert str(script_path) in command_full
            assert all(f"--{key}" in command_full for key in args_full.keys())
            assert all(str(value) in command_full for value in args_full.values())
            
            # Test 2: Minimal arguments  
            args_minimal = {"output_dir": pathlib.Path("/tmp/output")}
            command_minimal = runner._build_training_command(script_path, args_minimal)
            assert command_minimal.startswith("python")
            assert "--output_dir" in command_minimal
            assert "--job_configs_path" not in command_minimal
            
            # Test 3: No arguments
            command_empty = runner._build_training_command(script_path, {})
            parts = command_empty.split()
            assert len(parts) == 2
            assert parts[0] == "python"
            assert parts[1] == str(script_path)
            
            # Test 4: Special characters in paths
            args_special = {
                "job_configs_path": pathlib.Path("/tmp/job config with spaces.json"),
                "output_dir": pathlib.Path("/tmp/output-dir_with-special.chars")
            }
            command_special = runner._build_training_command(script_path, args_special)
            assert "job config with spaces.json" in command_special
            assert "output-dir_with-special.chars" in command_special
            
            logging.info("✅ All command building scenarios passed")

        finally:
            script_path.unlink(missing_ok=True) 

class TestCreateAndSubmitAzureJob:
    """Integration tests for _create_and_submit_azure_job method - consolidated."""


    def test_azure_job_submission_integration(self):
        """Comprehensive integration test for Azure ML job submission."""
        _check_azure_credentials()
        
        runner = _create_test_runner()
        ml_client = AzureMLClientFactory().create_ml_client()
        test_job = create_test_job()

        # compute_id = next(compute for compute in ml_client.compute.list()).name
        # if compute_id is None:
        compute_id = TestProvisionComputeIntegration().test_provision_gpu_performant_compute(to_cleanup = False)
        
        test_id = str(uuid.uuid4())[:8]
        job_id = f"test-job-{test_id}"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # TODO can this block be replace with _create_test_script
            code_dir = pathlib.Path(temp_dir) / "code"
            code_dir.mkdir(parents=True, exist_ok=True)
            
            # Create minimal test script
            script_content = '''#!/usr/bin/env python3
import sys
print("Azure ML test job executed!")
sys.exit(0)
'''
            entrypoint_script = code_dir / "train.py"
            entrypoint_script.write_text(script_content)
            entrypoint_script.chmod(0o755)
            
            command = f"python {entrypoint_script.name}"
            
            try:
                # Submit job
                azure_job_name = runner._create_and_submit_azure_job(
                    job_id=job_id,
                    ml_client=ml_client,
                    compute_label=compute_id,
                    environment_label="azureml://registries/azureml/environments/acpt-pytorch-2.2-cuda12.1/versions/37",
                    command=command,
                    code_dir=code_dir,
                    surrogate_job=test_job
                )
                
                # Verify submission
                assert azure_job_name is not None
                assert isinstance(azure_job_name, str)
                assert len(azure_job_name) > 0
                
                # Verify job metadata
                submitted_job = ml_client.jobs.get(azure_job_name)
                assert submitted_job.name == azure_job_name
                assert submitted_job.experiment_name == test_job.metadata.experiment_name
                
                # Verify tags
                assert submitted_job.tags is not None
                assert submitted_job.tags["user_reference"] == test_job.metadata.user_reference
                assert submitted_job.tags["atlas_reference"] == test_job.metadata.atlas_reference
                assert submitted_job.tags["algorithm"] == test_job.metadata.surrogate_algo.value
                
                logging.info(f"✅ Successfully submitted and verified Azure ML job: {azure_job_name}")
                
            except Exception as e:
                logging.error(f"❌ Azure job submission test failed: {str(e)}")
                raise

    def test_azure_job_error_handling(self):
        """Test error handling with invalid parameters."""
        _check_azure_credentials()
        
        runner = _create_test_runner()
        ml_client = AzureMLClientFactory().create_ml_client()
        test_job = create_test_job()
        
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            code_dir = pathlib.Path(temp_dir) / "code"
            code_dir.mkdir(parents=True)
            
            entrypoint_script = code_dir / "train.py"
            entrypoint_script.write_text("print('test')")
            entrypoint_script.chmod(0o755)
            
            command = f"python {entrypoint_script.name}"
            
            # Test with invalid compute target - should raise exception
            with pytest.raises(Exception):
                runner._create_and_submit_azure_job(
                    job_id=f"test-error-{str(uuid.uuid4())[:8]}",
                    ml_client=ml_client,
                    compute_label="nonexistent-compute-target",
                    environment_label="AzureML-pytorch-2.0-ubuntu20.04-py38-cuda11-gpu",
                    command=command,
                    code_dir=code_dir,
                    surrogate_job=test_job
                )
            
            logging.info("✅ Error handling test passed - invalid compute target properly rejected")

class TestDepickleRawModelAndJobEntity:
    """Integration tests for _depickle_rawmodel_and_jobentity method."""
    def test_depickle_rawmodel_and_jobentity(self):
        """Test depickling of raw model and job entity."""
        runner = _create_test_runner()
        
        # Create test data
        model = create_simple_torch_model()
        job = create_test_job()
        
        # Create test directory and pickle files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_dir = pathlib.Path(temp_dir)
            
            # Save model using torch.save with protocol 4
            model_path = temp_dir / "raw_model.pth"
            torch.save(model, model_path, _use_new_zipfile_serialization=True)
            
            # Save job entity using pickle with protocol 4
            job_path = temp_dir / "training_job.pkl"
            with open(job_path, "wb") as f:
                pickle.dump(job, f, protocol=4)
            
            # Test depickling
            raw_model, job_entity = runner._depickle_rawmodel_and_jobentity(
                temp_dir,
                ["raw_model"],
                ["training_job"]
            )
            
            # Verify model was loaded correctly
            assert isinstance(raw_model, nn.Module)
            assert isinstance(raw_model, SimpleModel)
            
            # Compare model parameters
            for p1, p2 in zip(raw_model.parameters(), model.parameters()):
                assert torch.allclose(p1, p2)
                
            # Verify job entity was loaded correctly
            assert job_entity is not None
            assert isinstance(job_entity, su.ENTTrainingJob)
            assert job_entity.uid == job.uid

            logging.info("✅ Depickling test passed")    




class TestPollForJobArtefacts:
    """
    Integration tests for _poll_and_download_rawmodel_and_jobentity method.

    Tests the complete workflow of:
    1. Submitting a real Azure ML job with complex training script
    2. Polling for job completion with timeout
    3. Downloading artifacts when job completes
    4. Validating downloaded artifacts contain expected files
    """



    def test_poll_and_download_job_artifacts_integration(self):
        """
        Integration test for complete job polling and artifact download workflow.

        This test:
        1. Creates a simple training script focused on testing polling and download
        2. Submits a real Azure ML job
        3. Polls for completion with 20-minute timeout
        4. Downloads and validates artifacts
        """
        # Arrange: Check Azure credentials
        _check_azure_credentials()

        # Create test components
        runner = _create_test_runner()
        ml_client = AzureMLClientFactory().create_ml_client()

        # Create test data
        test_job = create_test_job()
        test_model = create_simple_torch_model()

        # Generate unique identifiers for this test
        test_id = str(uuid.uuid4())[:8]
        job_id = f"test-poll-job-{test_id}"
        environment_label="azureml://registries/azureml/environments/acpt-pytorch-2.2-cuda12.1/versions/37"
        compute_label = "test-gpu-perf-f5bc5b45" # hardcoded with know compute:

        # Create temporary directories
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = pathlib.Path(temp_dir)
            code_dir = temp_path / "code"
            output_dir = temp_path / "output"
            code_dir.mkdir(parents=True, exist_ok=True)
            output_dir.mkdir(parents=True, exist_ok=True)

            try:
                # Create simple training script for testing polling and download
                script_path = create_simple_training_script()

                # Copy script to code directory
                entrypoint_script = code_dir / "train.py"
                entrypoint_script.write_text(script_path.read_text())
                entrypoint_script.chmod(0o755)

                # Clean up temporary script
                script_path.unlink(missing_ok=True)

                # Build command
                command = f"python train.py --output_dir ./outputs"

                # Submit Azure ML job
                logging.info(f"Submitting Azure ML job for polling test: {job_id}")
                azure_job_name = runner._create_and_submit_azure_job(
                    job_id=job_id,
                    ml_client=ml_client,
                    compute_label=compute_label,
                    environment_label=environment_label,
                    command=command,
                    code_dir=code_dir,
                    surrogate_job=test_job
                )

                logging.info(f"✅ Job submitted successfully: {azure_job_name}")

                # Act: Poll for job completion and download artifacts (20-minute timeout)
                import time
                start_time = time.time()
                timeout_seconds = 1200  # 20 minutes
                logging.info(f"Starting polling for job artifacts with {timeout_seconds/60:.1f} minute timeout")

                # Poll with timeout handling
                downloaded_dir = None

                try:
                    # Start polling in a way that respects timeout
                    downloaded_dir = runner._poll_and_download_rawmodel_and_jobentity(
                        ml_client=ml_client,
                        azure_job_name=azure_job_name,
                        runner_output_dir=pathlib.Path("."),  # Use default Azure ML output structure
                        local_output_dir=output_dir,
                        polling_interval_s=5
                    )

                    elapsed_time = time.time() - start_time
                    logging.info(f"✅ Job completed and artifacts downloaded in {elapsed_time:.1f} seconds")

                except Exception as e:
                    elapsed_time = time.time() - start_time
                    if elapsed_time > timeout_seconds:
                        pytest.fail(f"Test timed out after {timeout_seconds/60:.1f} minutes")
                    else:
                        raise e

                # Assert: Verify artifacts were downloaded correctly
                assert downloaded_dir.exists()
                assert downloaded_dir == output_dir

                # Check for expected files from simple training script
                expected_files = [
                    "raw_model.pth",      # PyTorch state dict (best practice)
                    "raw_model_full.pth", # Full PyTorch model
                    "raw_model.pkl",      # Pickle model (compatibility)
                    "training_job.json",  # Job data as JSON (simplified)
                    "training.log",       # Training log
                    "metadata.json"       # Metadata file
                ]

                # Azure ML downloads files into artifacts/outputs/ subdirectory
                # artifacts_dir = downloaded_dir / "artifacts" / "outputs"
                artifacts_dir = downloaded_dir
                for expected_file in expected_files:
                    file_path = artifacts_dir / expected_file
                    assert file_path.exists(), f"Expected file not found: {expected_file} (looked in {file_path})"
                    assert file_path.stat().st_size > 0, f"File is empty: {expected_file}"

                logging.info(f"✅ All expected artifacts found: {expected_files}")

                # Verify PyTorch state dict can be loaded
                state_dict_path = artifacts_dir / "raw_model.pth"
                loaded_state_dict = torch.load(state_dict_path, map_location='cpu')
                assert isinstance(loaded_state_dict, dict)
                assert len(loaded_state_dict) > 0
                logging.info("✅ PyTorch state dict loaded successfully")

                # Verify full PyTorch model file exists (skip loading due to class reference issues in test environment)
                full_model_path = artifacts_dir / "raw_model_full.pth"
                assert full_model_path.exists(), "Full PyTorch model file should exist"
                assert full_model_path.stat().st_size > 0, "Full PyTorch model file should not be empty"
                logging.info("✅ Full PyTorch model file verified (exists and non-empty)")

                # Verify model info file can be loaded (simplified format)
                model_path = artifacts_dir / "raw_model.pkl"
                with open(model_path, "r") as f:
                    model_info = f.read()
                assert "SimpleModel" in model_info
                assert "Parameters:" in model_info
                logging.info("✅ Model info loaded successfully")

                # Verify job data can be loaded (JSON format)
                job_path = artifacts_dir / "training_job.json"
                with open(job_path, "r") as f:
                    loaded_job_data = json.load(f)
                assert isinstance(loaded_job_data, dict)
                assert loaded_job_data.get("uid") == "test-job-12345"
                logging.info("✅ Job data loaded successfully")

                # Verify metadata file
                metadata_path = artifacts_dir / "metadata.json"
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)
                assert metadata["status"] == "completed"
                assert metadata["job_uid"] == "test-job-12345"  # Simplified test script uses fixed UID
                assert len(metadata["files_created"]) == 6
                logging.info("✅ Metadata validated successfully")

                # Verify training log exists and has content
                log_path = artifacts_dir / "training.log"
                log_content = log_path.read_text()
                assert "Training Script Started" in log_content
                assert "Training Script Completed Successfully" in log_content
                logging.info("✅ Training log validated successfully")

                logging.info("✅ All artifact validation completed successfully")

            except Exception as e:
                logging.error(f"❌ Poll and download integration test failed: {str(e)}")
                raise

    def test_poll_and_download_with_job_failure_handling(self):
        """
        Test polling behavior when Azure ML job fails.

        This test submits a job that will fail and verifies that the polling
        method correctly detects the failure and raises appropriate exceptions.
        """
        # Arrange: Check Azure credentials
        _check_azure_credentials()

        # Create test components
        runner = _create_test_runner()
        ml_client = AzureMLClientFactory().create_ml_client()
        test_job = create_test_job()

        # Generate unique identifiers for this test
        test_id = str(uuid.uuid4())[:8]
        job_id = f"test-fail-job-{test_id}"
        compute_label= TestProvisionComputeIntegration().test_provision_gpu_performant_compute(to_cleanup = False)
        environment_label="azureml://registries/azureml/environments/acpt-pytorch-2.2-cuda12.1/versions/37"

        # Create temporary directories
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = pathlib.Path(temp_dir)
            code_dir = temp_path / "code"
            output_dir = temp_path / "output"
            code_dir.mkdir(parents=True, exist_ok=True)
            output_dir.mkdir(parents=True, exist_ok=True)

            try:
                # Create a script that will fail
                failing_script_content = '''#!/usr/bin/env python3
import sys
print("This script is designed to fail for testing purposes")
sys.exit(1)  # Exit with error code
'''

                entrypoint_script = code_dir / "fail_train.py"
                entrypoint_script.write_text(failing_script_content)
                entrypoint_script.chmod(0o755)

                # Build command
                command = "python fail_train.py"

                # Submit Azure ML job that will fail
                logging.info(f"Submitting failing Azure ML job: {job_id}")
                azure_job_name = runner._create_and_submit_azure_job(
                    job_id=job_id,
                    ml_client=ml_client,
                    compute_label=compute_label,
                    environment_label=environment_label,
                    command=command,
                    code_dir=code_dir,
                    surrogate_job=test_job
                )

                logging.info(f"✅ Failing job submitted: {azure_job_name}")

                # Act & Assert: Expect polling to raise SurrogateInfrastructureError
                from backend.core.interfaces.surrogate_interfaces import SurrogateInfrastructureError

                with pytest.raises(SurrogateInfrastructureError) as exc_info:
                    runner._poll_and_download_rawmodel_and_jobentity(
                        ml_client=ml_client,
                        azure_job_name=azure_job_name,
                        runner_output_dir=pathlib.Path("outputs"),
                        local_output_dir=output_dir,
                        polling_interval_s=5  # Poll every 5 seconds for faster failure detection
                    )

                # Verify the error message contains job information
                error_message = str(exc_info.value)
                assert azure_job_name in error_message
                assert "failed" in error_message.lower()

                logging.info(f"✅ Job failure correctly detected: {error_message}")

            except Exception as e:
                # If it's not the expected SurrogateInfrastructureError, re-raise
                from backend.core.interfaces.surrogate_interfaces import SurrogateInfrastructureError
                if not isinstance(e, SurrogateInfrastructureError):
                    logging.error(f"❌ Unexpected error in failure handling test: {str(e)}")
                    raise

    def test_debug_job_logs(self):
        """
        Debug test to check job logs and understand why jobs are failing.
        This test creates a minimal job and examines the logs.
        """
        _check_azure_credentials()

        # Create ML client
        ml_client = AzureMLClientFactory().create_ml_client()

        # Generate unique identifiers for this test
        test_id = str(uuid.uuid4())[:8]
        job_id = f"debug-job-{test_id}"

        # Use minimal environment and simple script
        environment_label = "azureml://registries/azureml/environments/acpt-pytorch-2.2-cuda12.1/versions/37"
        compute_label = "serverless"  # Use generic serverless

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = pathlib.Path(temp_dir)
            code_dir = temp_path / "code"
            code_dir.mkdir(parents=True, exist_ok=True)

            # Create minimal test script
            script_content = '''#!/usr/bin/env python3
import sys
import os
import torch
print("=== DEBUG SCRIPT START ===")
print(f"Python version: {sys.version}")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"Current working directory: {os.getcwd()}")
print(f"Environment variables:")
for key, value in os.environ.items():
    if 'AZURE' in key or 'ML' in key:
        print(f"  {key}: {value}")
print("=== DEBUG SCRIPT END ===")
'''

            script_file = code_dir / "debug.py"
            script_file.write_text(script_content)
            script_file.chmod(0o755)

            # Create test job
            test_job = create_test_job()
            # Note: Using existing job UID, job_id is just for Azure ML naming

            try:
                # Submit job
                runner = _create_test_runner()
                azure_job_name = runner._create_and_submit_azure_job(
                    job_id=job_id,
                    ml_client=ml_client,
                    compute_label=compute_label,
                    environment_label=environment_label,
                    command="python debug.py",
                    code_dir=code_dir,
                    surrogate_job=test_job
                )

                logging.info(f"Debug job submitted: {azure_job_name}")

                # Wait for job to complete (or fail) with shorter timeout
                import time
                timeout_seconds = 300  # 5 minutes
                start_time = time.time()

                while time.time() - start_time < timeout_seconds:
                    job = ml_client.jobs.get(azure_job_name)
                    status = job.status.lower()
                    logging.info(f"Debug job status: {status}")

                    if status in ['completed', 'failed', 'canceled']:
                        break

                    time.sleep(10)

                # Get final job details
                final_job = ml_client.jobs.get(azure_job_name)
                logging.info(f"Final job status: {final_job.status}")
                logging.info(f"Job compute: {getattr(final_job, 'compute', 'N/A')}")
                logging.info(f"Job resources: {getattr(final_job, 'resources', 'N/A')}")

                # Try to get job logs if available
                try:
                    # Note: This might not work depending on job status
                    logs = ml_client.jobs.stream(azure_job_name)
                    logging.info("Job logs:")
                    for log_line in logs:
                        logging.info(f"  {log_line}")
                except Exception as log_error:
                    logging.warning(f"Could not retrieve logs: {log_error}")

            except Exception as e:
                logging.error(f"Debug test failed: {str(e)}")
                # Don't raise - this is just for debugging


# ==================================================
# TEST RUNNER
# ==================================================


if __name__ == "__main__":
    # Configure root logger with console handler
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # Clear any existing handlers
    root_logger.handlers = []
    
    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(logging.Formatter('%(asctime)s | %(levelname)s | %(name)s - %(message)s'))
    root_logger.addHandler(console_handler)
    

    print("Running tests...")

    # --------------------------------
    # Run pytest on the current file
    # UNCOMMENT TO RUN
    # file_path = __file__
    # pytest.main(["-v", file_path])
    # print("done with tests")
    # --------------------------------

    # --------------------------------
    # MANUAL VALIDATION OF COMPUTE INSTANCE CRATION
    # UNCOMMENT TO RUN
    # compute_test = TestProvisionComputeIntegration()
    # compute_test.test_provision_cpu_budget_compute(trigger = False) # note: fails because of quotas
    # compute_test.test_provision_cpu_performant_compute(trigger = False) # note: fails because of quotas
    # compute_test.test_provision_gpu_budget_compute(trigger = False)
    # compute_test.test_provision_gpu_performant_compute(trigger = False)
    # --------------------------------
    
    # --------------------------------
    # MANUAL VALIDATION OF ENVIRONMENT CRATION
    # UNCOMMENT TO RUN
    # environment_test = TestProvisionEnvironmentIntegration()
    # environment_test.test_setup_curated_pytorch_environment()
    # --------------------------------
    
    # --------------------------------
    # MANUAL VALIDATION OF TRAINING COMMAND BUILDING
    # UNCOMMENT TO RUN
    # test=TestBuildTrainingCommand()
    # test.test_build_command_comprehensive()
    # --------------------------------

    # --------------------------------
    # MANUAL VALIDATION OF JOB SUBMISSION
    # UNCOMMENT TO RUN
    # test = TestCreateAndSubmitAzureJob()
    # test.test_azure_job_submission_integration()
    # test.test_azure_job_error_handling()
    # --------------------------------

    # --------------------------------
    # MANUAL VALIDATION OF DEPICKLE
    #    # UNCOMMENT TO RUN
    # test =  TestDepickleRawModelAndJobEntity()
    # test.test_depickle_rawmodel_and_jobentity()
    # --------------------------------
    
    # MANUAL VALIDATION OF POLLING
    test = TestPollForJobArtefacts()
    test.test_poll_and_download_job_artifacts_integration()
    

    print("Tests complete")
