from backend.core.interfaces.logging_interfaces import ILogging
from backend.infrastructure import *
from backend.core._sharedutils.singleton import SingletonMeta
from enum import Enum
from typing import Dict, Type, Any
import os
from threading import Lock
from backend.core import *


class Environment(Enum):
    DEVELOPMENT = "development"
    UAT = "uat"
    # STAGING = "staging"
    PRODUCTION = "production"
    # TESTING = "testing"
    # LOCAL = "local"


class AdaptorFactory(metaclass=SingletonMeta):
    """Thread safe Adaptor Factory using singleton pattern"""

    def __init__(self):
        self._lock = Lock()  # Instance-level lock for service creation
        self._adaptor_instances: Dict[str, Any] = {}
        self._adaptor_map: Dict[str, Dict[str, Type]] = {
            Environment.PRODUCTION.value: {
                IMatrix.__name__: MatrixPrefectRunner,
                IAtlas.__name__: AtlasPostgreAdaptor,
                IMetis.__name__: LocalMetisProduction,
                ILogging.__name__: ConsoleLogger,
                IAthena.__name__: LocalAthenaProduction,
                ISurrogate.__name__: LocalSurrogate,
                IOptimizer.__name__: ComprehensiveOptimizer
            },
            Environment.UAT.value: {
                IMatrix.__name__: MatrixPrefectRunner,
                IAtlas.__name__: AtlasPostgreAdaptor,
                IMetis.__name__: LocalMetisProduction,
                ILogging.__name__: ConsoleLogger,
                IAthena.__name__: LocalAthenaProduction,
                ISurrogate.__name__: LocalSurrogate,
                IOptimizer.__name__: ComprehensiveOptimizer
            },
            Environment.DEVELOPMENT.value: {
                IMatrix.__name__: MatrixLocalSerialRunner,
                IAtlas.__name__: AtlasInMemAdaptor,
                IMetis.__name__: LocalMetisDev,
                ILogging.__name__: ConsoleLogger,
                IAthena.__name__: LocalAthenaProduction,
                ISurrogate.__name__: LocalSurrogate,
                IOptimizer.__name__: BasicOptimizer
            },
        }

    def get_adaptor(self, adaptor_name: str) -> Any:
        """Factory function to get appropriate service implementation based on profile"""
        if adaptor_name in self._adaptor_instances:
            return self._adaptor_instances[adaptor_name]

        with self._lock:
            # Double check pattern to ensure synchronisation across mutliple threads
            if adaptor_name in self._adaptor_instances:
                return self._adaptor_instances[adaptor_name]

            profile = os.getenv("PROFILE", Environment.DEVELOPMENT.value)

            # Get implementation class based on profile
            implementations = self._adaptor_map.get(
                profile, self._adaptor_map[Environment.DEVELOPMENT.value]
            )

            service_class = implementations.get(adaptor_name)
            if not service_class:
                raise ValueError(
                    f"No implementation defined for service {adaptor_name} in profile {profile}"
                )

            # Instantiate concrete class
            self._adaptor_instances[adaptor_name] = service_class()

            return self._adaptor_instances[adaptor_name]
